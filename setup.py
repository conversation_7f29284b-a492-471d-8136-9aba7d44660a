#!/usr/bin/env python3
"""
Setup script for CodeLlama Custom Setup
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
readme_file = Path(__file__).parent / "README.md"
if readme_file.exists():
    with open(readme_file, "r", encoding="utf-8") as f:
        long_description = f.read()
else:
    long_description = "A customizable CodeLlama installation with flexible configuration options for code generation tasks."

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, "r", encoding="utf-8") as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]
else:
    requirements = [
        "torch>=2.0.0",
        "transformers>=4.30.0",
        "accelerate>=0.20.0",
        "pyyaml>=6.0",
        "click>=8.0.0",
        "rich>=13.0.0",
        "prompt-toolkit>=3.0.0",
        "tqdm>=4.65.0",
        "numpy>=1.24.0",
        "pydantic>=2.0.0",
        "jsonschema>=4.17.0"
    ]

setup(
    name="codellama-setup",
    version="1.0.0",
    author="CodeLlama Setup",
    author_email="",
    description="A customizable CodeLlama installation with flexible configuration options",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Code Generators",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "optional": [
            "bitsandbytes>=0.39.0",
            "sentence-transformers>=2.2.0",
            "faiss-cpu>=1.7.0",
            "gradio>=3.35.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "codellama-cli=scripts.cli:cli",
            "codellama-chat=scripts.interactive_chat:main",
            "codellama-batch=scripts.batch_process:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["config/*.yaml", "config/examples/*.yaml", "config/schemas/*.json"],
    },
    zip_safe=False,
    keywords="codellama, code generation, ai, machine learning, transformers",
    project_urls={
        "Documentation": "",
        "Source": "",
        "Tracker": "",
    },
)
