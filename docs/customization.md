# Customization Guide

This guide covers advanced customization techniques for the CodeLlama setup, including extending functionality, creating custom components, and integrating with external tools.

## Overview

The CodeLlama setup is designed to be highly customizable. You can:

- Create custom prompt templates
- Extend the wrapper class with new methods
- Add custom preprocessing and postprocessing
- Integrate with external tools and APIs
- Create domain-specific configurations
- Build custom interfaces

## Custom Prompt Templates

### Creating New Templates

Add custom templates to your configuration:

```yaml
prompts:
  # Custom template for code reviews
  code_review_template: |
    {system_prompt}
    
    Please review the following {language} code:
    
    ```{language}
    {code}
    ```
    
    Provide feedback on:
    1. Code quality and style
    2. Performance optimizations
    3. Security considerations
    4. Best practices adherence
    
    Code Review:
  
  # Custom template for refactoring
  refactor_template: |
    {system_prompt}
    
    Refactor the following code to improve:
    {goals}
    
    Original code:
    ```{language}
    {code}
    ```
    
    Refactored code:
  
  # Custom template for documentation
  documentation_template: |
    {system_prompt}
    
    Generate comprehensive documentation for:
    
    ```{language}
    {code}
    ```
    
    Include:
    - Function/class descriptions
    - Parameter explanations
    - Return value descriptions
    - Usage examples
    - Error conditions
    
    Documentation:
```

### Using Custom Templates

```python
from codellama_wrapper import CodeLlamaWrapper

wrapper = CodeLlamaWrapper("my_custom_config.yaml")

# Use custom template
code_to_review = "def process_data(data): return [x*2 for x in data]"

review_prompt = wrapper.config["prompts"]["code_review_template"].format(
    system_prompt=wrapper.config["prompts"]["system_prompt"],
    code=code_to_review,
    language="python"
)

result = wrapper.generate(review_prompt)
```

## Extending the Wrapper Class

### Creating a Custom Wrapper

```python
from codellama_wrapper import CodeLlamaWrapper
from utils import extract_code_from_response, validate_code_syntax

class CustomCodeLlamaWrapper(CodeLlamaWrapper):
    """Extended wrapper with custom functionality."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.custom_stats = {
            "code_reviews": 0,
            "refactorings": 0,
            "validations": 0
        }
    
    def review_code(self, code: str, language: str = None, focus_areas: list = None):
        """Custom method for code reviews."""
        if language is None:
            language = self._detect_language(code)
        
        focus_text = ""
        if focus_areas:
            focus_text = f"Focus especially on: {', '.join(focus_areas)}"
        
        template = self.config["prompts"].get("code_review_template", 
                                             self.config["prompts"]["code_explanation_template"])
        
        prompt = template.format(
            system_prompt=self.config["prompts"]["system_prompt"],
            code=code,
            language=language,
            focus=focus_text
        )
        
        result = self.generate(prompt)
        self.custom_stats["code_reviews"] += 1
        
        return result
    
    def refactor_code(self, code: str, goals: str, language: str = None):
        """Custom method for code refactoring."""
        if language is None:
            language = self._detect_language(code)
        
        template = self.config["prompts"].get("refactor_template", 
                                             self.config["prompts"]["code_generation_template"])
        
        prompt = template.format(
            system_prompt=self.config["prompts"]["system_prompt"],
            code=code,
            goals=goals,
            language=language
        )
        
        result = self.generate(prompt)
        self.custom_stats["refactorings"] += 1
        
        return result
    
    def generate_with_validation(self, prompt: str, **kwargs):
        """Generate code and validate syntax."""
        result = self.generate(prompt, **kwargs)
        
        # Extract and validate code blocks
        code_blocks = extract_code_from_response(result.text)
        validation_results = []
        
        for language, code in code_blocks:
            is_valid, error = validate_code_syntax(code, language)
            validation_results.append({
                "language": language,
                "code": code,
                "valid": is_valid,
                "error": error
            })
        
        self.custom_stats["validations"] += 1
        
        # Add validation info to result
        result.validation_results = validation_results
        result.has_valid_code = any(v["valid"] for v in validation_results)
        
        return result
    
    def get_custom_stats(self):
        """Get custom statistics."""
        base_stats = self.get_stats()
        base_stats.update(self.custom_stats)
        return base_stats
```

### Using the Custom Wrapper

```python
# Use the custom wrapper
custom_wrapper = CustomCodeLlamaWrapper()

# Use custom methods
code = "def calculate(x, y): return x + y"
review = custom_wrapper.review_code(code, focus_areas=["performance", "error handling"])

refactored = custom_wrapper.refactor_code(
    code, 
    goals="Add type hints, error handling, and documentation"
)

# Generate with validation
validated_result = custom_wrapper.generate_with_validation(
    "Create a function to parse JSON safely"
)

if validated_result.has_valid_code:
    print("Generated valid code!")
else:
    print("Generated code has syntax issues")

# Get extended stats
stats = custom_wrapper.get_custom_stats()
print(f"Code reviews: {stats['code_reviews']}")
```

## Custom Preprocessing and Postprocessing

### Input Preprocessing

```python
class PreprocessingWrapper(CodeLlamaWrapper):
    """Wrapper with custom input preprocessing."""
    
    def preprocess_prompt(self, prompt: str, task_type: str = "general") -> str:
        """Preprocess prompts based on task type."""
        
        # Add context based on task type
        if task_type == "security":
            prompt = f"[SECURITY FOCUS] {prompt}\nEnsure the code follows security best practices."
        elif task_type == "performance":
            prompt = f"[PERFORMANCE FOCUS] {prompt}\nOptimize for speed and efficiency."
        elif task_type == "maintainability":
            prompt = f"[MAINTAINABILITY FOCUS] {prompt}\nWrite clean, readable, well-documented code."
        
        # Add common instructions
        prompt += "\n\nPlease include appropriate comments and error handling."
        
        return prompt
    
    def generate_code(self, description: str, language: str = None, 
                     task_type: str = "general", **kwargs):
        """Generate code with preprocessing."""
        
        # Preprocess the description
        processed_description = self.preprocess_prompt(description, task_type)
        
        # Call parent method with processed input
        return super().generate_code(processed_description, language, **kwargs)
```

### Output Postprocessing

```python
class PostprocessingWrapper(CodeLlamaWrapper):
    """Wrapper with custom output postprocessing."""
    
    def postprocess_result(self, result, add_metadata: bool = True):
        """Postprocess generation results."""
        
        # Extract code blocks
        code_blocks = extract_code_from_response(result.text)
        
        # Add metadata
        if add_metadata:
            metadata = f"""
# Generation Metadata
# Model: {result.model_name}
# Tokens: {result.token_count}
# Time: {result.generation_time:.2f}s
# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

"""
            result.text = metadata + result.text
        
        # Validate code blocks
        for i, (language, code) in enumerate(code_blocks):
            is_valid, error = validate_code_syntax(code, language)
            if not is_valid:
                result.text += f"\n\n# Warning: Code block {i+1} has syntax issues: {error}"
        
        return result
    
    def generate(self, prompt: str, **kwargs):
        """Generate with postprocessing."""
        result = super().generate(prompt, **kwargs)
        return self.postprocess_result(result)
```

## Domain-Specific Customizations

### Web Development Wrapper

```python
class WebDevWrapper(CodeLlamaWrapper):
    """Specialized wrapper for web development."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Web-specific prompt additions
        self.web_context = """
You are a web development expert. Consider:
- Security (XSS, CSRF, SQL injection prevention)
- Performance (caching, optimization)
- Accessibility (WCAG guidelines)
- Modern web standards
- Cross-browser compatibility
"""
    
    def create_api_endpoint(self, description: str, framework: str = "flask"):
        """Create API endpoint code."""
        prompt = f"""
{self.web_context}

Create a {framework} API endpoint for: {description}

Include:
- Proper HTTP methods and status codes
- Input validation
- Error handling
- Security measures
- Documentation

API endpoint code:
"""
        return self.generate(prompt)
    
    def create_frontend_component(self, description: str, framework: str = "react"):
        """Create frontend component code."""
        prompt = f"""
{self.web_context}

Create a {framework} component for: {description}

Include:
- Proper component structure
- State management
- Event handling
- Accessibility features
- Responsive design considerations

Component code:
"""
        return self.generate(prompt)
    
    def review_security(self, code: str, language: str = None):
        """Security-focused code review."""
        prompt = f"""
{self.web_context}

Perform a security review of this code:

```{language or 'javascript'}
{code}
```

Check for:
- Input validation issues
- Authentication/authorization flaws
- Data exposure risks
- Injection vulnerabilities
- Insecure configurations

Security review:
"""
        return self.generate(prompt)
```

### Data Science Wrapper

```python
class DataScienceWrapper(CodeLlamaWrapper):
    """Specialized wrapper for data science tasks."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.ds_context = """
You are a data science expert. Consider:
- Data validation and cleaning
- Statistical significance
- Visualization best practices
- Performance with large datasets
- Reproducibility
- Documentation of assumptions
"""
    
    def create_analysis(self, description: str, data_info: str = ""):
        """Create data analysis code."""
        prompt = f"""
{self.ds_context}

Create Python code for data analysis: {description}

Data information: {data_info}

Include:
- Data loading and validation
- Exploratory data analysis
- Statistical analysis
- Visualizations
- Interpretation of results

Analysis code:
"""
        return self.generate(prompt)
    
    def create_model(self, description: str, model_type: str = "classification"):
        """Create machine learning model code."""
        prompt = f"""
{self.ds_context}

Create a {model_type} model for: {description}

Include:
- Data preprocessing
- Feature engineering
- Model selection and training
- Evaluation metrics
- Cross-validation
- Model interpretation

Model code:
"""
        return self.generate(prompt)
```

## Integration with External Tools

### Git Integration

```python
import subprocess
from pathlib import Path

class GitIntegratedWrapper(CodeLlamaWrapper):
    """Wrapper with Git integration."""
    
    def generate_commit_message(self, diff: str = None):
        """Generate commit message from git diff."""
        if diff is None:
            # Get current diff
            result = subprocess.run(
                ["git", "diff", "--cached"], 
                capture_output=True, 
                text=True
            )
            diff = result.stdout
        
        prompt = f"""
Generate a concise, descriptive commit message for these changes:

```diff
{diff}
```

Follow conventional commit format:
- feat: new feature
- fix: bug fix
- docs: documentation
- style: formatting
- refactor: code restructuring
- test: adding tests

Commit message:
"""
        return self.generate(prompt)
    
    def review_pull_request(self, diff: str):
        """Review a pull request diff."""
        prompt = f"""
Review this pull request and provide feedback:

```diff
{diff}
```

Consider:
- Code quality
- Potential bugs
- Performance impact
- Security implications
- Test coverage

Pull request review:
"""
        return self.generate(prompt)
```

### IDE Integration

```python
class IDEWrapper(CodeLlamaWrapper):
    """Wrapper for IDE integration."""
    
    def complete_at_cursor(self, file_content: str, cursor_position: int):
        """Complete code at cursor position."""
        
        # Split content at cursor
        before_cursor = file_content[:cursor_position]
        after_cursor = file_content[cursor_position:]
        
        # Get context (last few lines)
        lines_before = before_cursor.split('\n')
        context_lines = lines_before[-10:]  # Last 10 lines
        context = '\n'.join(context_lines)
        
        prompt = f"""
Complete the code at the cursor position:

Context:
```python
{context}
<CURSOR>
{after_cursor[:100]}...
```

Completion:
"""
        
        result = self.generate(prompt, max_new_tokens=256)
        
        # Extract just the completion part
        completion = result.text.strip()
        if completion.startswith('```'):
            # Remove code block markers
            lines = completion.split('\n')
            completion = '\n'.join(lines[1:-1])
        
        return completion
    
    def suggest_improvements(self, selected_code: str, language: str):
        """Suggest improvements for selected code."""
        prompt = f"""
Suggest improvements for this {language} code:

```{language}
{selected_code}
```

Provide specific, actionable suggestions for:
- Performance optimization
- Code clarity
- Best practices
- Error handling

Suggestions:
"""
        return self.generate(prompt)
```

## Custom Configuration Schemas

### Creating Custom Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Custom CodeLlama Configuration Schema",
  "type": "object",
  "properties": {
    "custom_settings": {
      "type": "object",
      "properties": {
        "domain": {
          "type": "string",
          "enum": ["web", "data_science", "mobile", "embedded"],
          "description": "Domain specialization"
        },
        "code_style": {
          "type": "string",
          "enum": ["pep8", "google", "airbnb", "standard"],
          "description": "Code style preference"
        },
        "complexity_level": {
          "type": "string",
          "enum": ["beginner", "intermediate", "advanced", "expert"],
          "description": "Target complexity level"
        },
        "include_tests": {
          "type": "boolean",
          "default": false,
          "description": "Include unit tests in generated code"
        },
        "documentation_style": {
          "type": "string",
          "enum": ["sphinx", "google", "numpy", "minimal"],
          "description": "Documentation style preference"
        }
      }
    }
  }
}
```

### Using Custom Configuration

```yaml
# custom_config.yaml
model:
  name: "codellama/CodeLlama-7b-Python-hf"

generation:
  temperature: 0.1
  max_new_tokens: 1024

custom_settings:
  domain: "web"
  code_style: "pep8"
  complexity_level: "intermediate"
  include_tests: true
  documentation_style: "google"

prompts:
  system_prompt: |
    You are a {domain} development expert. Write {complexity_level}-level code
    following {code_style} style guidelines. Use {documentation_style} 
    documentation format. {include_tests_instruction}
```

## Performance Optimization

### Custom Caching Strategy

```python
import hashlib
import pickle
from pathlib import Path

class AdvancedCachingWrapper(CodeLlamaWrapper):
    """Wrapper with advanced caching capabilities."""
    
    def __init__(self, *args, cache_dir="./cache", **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def _get_cache_key(self, prompt: str, config: dict) -> str:
        """Generate cache key from prompt and config."""
        cache_data = {
            "prompt": prompt,
            "temperature": config.get("temperature", 0.1),
            "max_tokens": config.get("max_new_tokens", 512),
            "model": self.config["model"]["name"]
        }
        cache_string = str(sorted(cache_data.items()))
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _load_from_cache(self, cache_key: str):
        """Load result from cache."""
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        if cache_file.exists():
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        return None
    
    def _save_to_cache(self, cache_key: str, result):
        """Save result to cache."""
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        with open(cache_file, 'wb') as f:
            pickle.dump(result, f)
    
    def generate(self, prompt: str, **kwargs):
        """Generate with persistent caching."""
        # Create cache key
        config = {**self.generation_config.to_dict(), **kwargs}
        cache_key = self._get_cache_key(prompt, config)
        
        # Try to load from cache
        cached_result = self._load_from_cache(cache_key)
        if cached_result:
            return cached_result
        
        # Generate new result
        result = super().generate(prompt, **kwargs)
        
        # Save to cache
        self._save_to_cache(cache_key, result)
        
        return result
```

## Testing Custom Components

### Unit Tests for Custom Wrapper

```python
import unittest
from unittest.mock import Mock, patch

class TestCustomWrapper(unittest.TestCase):
    """Test cases for custom wrapper functionality."""
    
    def setUp(self):
        self.wrapper = CustomCodeLlamaWrapper()
        self.wrapper.model = Mock()  # Mock the model
        self.wrapper.tokenizer = Mock()
    
    def test_code_review(self):
        """Test code review functionality."""
        # Mock the generate method
        mock_result = Mock()
        mock_result.text = "This code looks good but could use error handling."
        
        with patch.object(self.wrapper, 'generate', return_value=mock_result):
            result = self.wrapper.review_code("def test(): pass")
            
            self.assertIsNotNone(result)
            self.assertEqual(self.wrapper.custom_stats["code_reviews"], 1)
    
    def test_validation(self):
        """Test code validation."""
        mock_result = Mock()
        mock_result.text = "```python\ndef valid_function():\n    return True\n```"
        
        with patch.object(self.wrapper, 'generate', return_value=mock_result):
            result = self.wrapper.generate_with_validation("Create a function")
            
            self.assertTrue(hasattr(result, 'validation_results'))
            self.assertTrue(hasattr(result, 'has_valid_code'))

if __name__ == '__main__':
    unittest.main()
```

## Best Practices for Customization

1. **Extend, Don't Modify**: Create new classes that extend the base wrapper rather than modifying core files
2. **Use Configuration**: Make customizations configurable through YAML files
3. **Add Validation**: Validate inputs and outputs in custom methods
4. **Document Changes**: Document custom functionality thoroughly
5. **Test Thoroughly**: Write tests for custom components
6. **Performance Monitoring**: Monitor performance impact of customizations
7. **Version Control**: Keep custom configurations and code in version control

## Next Steps

- Explore the `examples/` directory for more customization examples
- Review the source code in `src/` to understand the architecture
- Create your own domain-specific wrappers
- Contribute improvements back to the project
