# Installation Guide

This guide will walk you through installing and setting up the CodeLlama custom setup on your system.

## Prerequisites

### System Requirements

- **Python**: 3.8 or higher
- **Memory**: At least 8GB RAM (16GB+ recommended for larger models)
- **Storage**: 10GB+ free space for models and dependencies
- **GPU** (optional but recommended): NVIDIA GPU with CUDA support for faster inference

### Hardware Recommendations

| Model Size | RAM Required | GPU Memory | Performance |
|------------|--------------|------------|-------------|
| 7B         | 8GB          | 6GB+       | Good        |
| 13B        | 16GB         | 12GB+      | Better      |
| 34B        | 32GB         | 24GB+      | Best        |

## Installation Steps

### 1. Clone or Download the Setup

```bash
# If you have the setup as a repository
git clone <repository-url>
cd codellama-setup

# Or if you have the files locally, navigate to the directory
cd /path/to/codellama-setup
```

### 2. Create a Virtual Environment (Recommended)

```bash
# Create virtual environment
python -m venv codellama-env

# Activate virtual environment
# On Linux/Mac:
source codellama-env/bin/activate
# On Windows:
codellama-env\Scripts\activate
```

### 3. Install Dependencies

```bash
# Install core dependencies
pip install -r requirements.txt

# For development (optional)
pip install pytest black flake8 mypy

# For enhanced CLI experience (optional)
pip install rich
```

### 4. Verify Installation

```bash
# Test the installation
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import transformers; print(f'Transformers: {transformers.__version__}')"

# Check GPU availability (if you have one)
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

## Model Download

The models will be automatically downloaded when you first use them. However, you can pre-download them:

### Automatic Download (Recommended)

Models are downloaded automatically on first use. The default model is `codellama/CodeLlama-7b-Python-hf`.

### Manual Download (Optional)

```bash
# Using Hugging Face CLI (install with: pip install huggingface_hub)
huggingface-cli download codellama/CodeLlama-7b-Python-hf

# Or using Python
python -c "
from transformers import AutoTokenizer, AutoModelForCausalLM
tokenizer = AutoTokenizer.from_pretrained('codellama/CodeLlama-7b-Python-hf')
model = AutoModelForCausalLM.from_pretrained('codellama/CodeLlama-7b-Python-hf')
print('Model downloaded successfully')
"
```

## Configuration

### 1. Test Basic Setup

```bash
# Test with default configuration
python examples/basic_usage.py
```

### 2. Create Custom Configuration

```bash
# Create an example configuration
python scripts/cli.py create-config my_config.yaml

# Edit the configuration file
# nano my_config.yaml  # or your preferred editor
```

### 3. Test Custom Configuration

```bash
# Test with custom configuration
python scripts/cli.py --config my_config.yaml info
```

## Troubleshooting

### Common Issues

#### 1. Out of Memory Errors

**Problem**: `RuntimeError: CUDA out of memory` or similar memory errors.

**Solutions**:
```yaml
# In your config file, try these settings:
model:
  load_in_8bit: true  # Use 8-bit quantization
  # or
  load_in_4bit: true  # Use 4-bit quantization (even more memory efficient)

generation:
  max_new_tokens: 256  # Reduce max tokens
```

#### 2. Slow Performance

**Problem**: Generation is very slow.

**Solutions**:
- Use a GPU if available
- Try smaller models (7B instead of 13B/34B)
- Reduce `max_new_tokens` in configuration
- Enable caching in configuration

#### 3. Model Download Issues

**Problem**: Models fail to download or are corrupted.

**Solutions**:
```bash
# Clear cache and retry
rm -rf ~/.cache/huggingface/transformers/
python -c "from transformers import AutoTokenizer; AutoTokenizer.from_pretrained('codellama/CodeLlama-7b-Python-hf')"

# Or specify a different cache directory
export HF_HOME=/path/to/large/storage
```

#### 4. Import Errors

**Problem**: `ModuleNotFoundError` for src modules.

**Solutions**:
```bash
# Make sure you're in the correct directory
cd /path/to/codellama-setup

# Or add to Python path
export PYTHONPATH="${PYTHONPATH}:/path/to/codellama-setup/src"
```

### Performance Optimization

#### GPU Optimization

```yaml
# config.yaml
model:
  device: "cuda"  # Force GPU usage
  torch_dtype: "float16"  # Use half precision
  device_map: "auto"  # Automatic device mapping

performance:
  cleanup_after_generation: true  # Free memory after each generation
```

#### CPU Optimization

```yaml
# config.yaml
model:
  device: "cpu"
  torch_dtype: "float32"

generation:
  max_new_tokens: 256  # Smaller outputs for faster generation

performance:
  batch_size: 1
  enable_cache: true
  cache_size: 50
```

## Environment Variables

You can set these environment variables to customize behavior:

```bash
# Hugging Face cache directory
export HF_HOME=/path/to/cache

# CUDA device selection
export CUDA_VISIBLE_DEVICES=0

# Disable tokenizers parallelism warnings
export TOKENIZERS_PARALLELISM=false

# Set logging level
export CODELLAMA_LOG_LEVEL=INFO
```

## Docker Installation (Optional)

If you prefer using Docker:

```dockerfile
# Dockerfile
FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set Python path
ENV PYTHONPATH=/app/src

# Default command
CMD ["python", "scripts/interactive_chat.py"]
```

```bash
# Build and run
docker build -t codellama-setup .
docker run -it --gpus all codellama-setup
```

## Next Steps

After successful installation:

1. **Read the Configuration Guide**: [configuration.md](configuration.md)
2. **Try the Examples**: Run the example scripts in the `examples/` directory
3. **Explore the CLI**: Use `python scripts/cli.py --help` to see available commands
4. **Start the Interactive Chat**: `python scripts/interactive_chat.py`

## Getting Help

If you encounter issues:

1. Check the [troubleshooting section](#troubleshooting) above
2. Review the [configuration guide](configuration.md)
3. Look at the example configurations in `config/examples/`
4. Check the system requirements and ensure your hardware is compatible

## Uninstallation

To remove the setup:

```bash
# Deactivate virtual environment
deactivate

# Remove virtual environment
rm -rf codellama-env

# Remove downloaded models (optional)
rm -rf ~/.cache/huggingface/transformers/

# Remove the setup directory
rm -rf /path/to/codellama-setup
```
