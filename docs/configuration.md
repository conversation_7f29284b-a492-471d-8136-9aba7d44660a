# Configuration Guide

This guide explains how to configure the CodeLlama setup for your specific needs.

## Configuration Overview

The CodeLlama setup uses YAML configuration files to control model behavior, generation parameters, and system settings. The configuration system is hierarchical and supports:

- **Default Configuration**: Base settings in `config/default_config.yaml`
- **Custom Configurations**: Your own configuration files
- **Mode-based Overrides**: Different settings for different use cases
- **Runtime Overrides**: Command-line parameter overrides

## Configuration File Structure

```yaml
# Model settings
model:
  name: "codellama/CodeLlama-7b-Python-hf"
  device: "auto"
  torch_dtype: "float16"

# Generation parameters
generation:
  max_new_tokens: 512
  temperature: 0.1
  top_p: 0.95

# Prompt templates
prompts:
  system_prompt: "You are a helpful coding assistant..."
  
# Output formatting
formatting:
  include_explanations: true
  default_language: "python"

# Performance settings
performance:
  enable_cache: true
  cleanup_after_generation: true

# Safety settings
safety:
  filter_harmful_content: true
  warn_dangerous_code: true

# Custom modes
modes:
  creative:
    temperature: 0.8
    max_new_tokens: 1024
```

## Model Configuration

### Model Selection

```yaml
model:
  # Choose your model
  name: "codellama/CodeLlama-7b-Python-hf"  # Python-focused
  # name: "codellama/CodeLlama-7b-hf"        # General purpose
  # name: "codellama/CodeLlama-13b-hf"       # Larger, better quality
  # name: "codellama/CodeLlama-34b-hf"       # Largest, best quality
```

### Device Configuration

```yaml
model:
  device: "auto"        # Automatic device selection
  # device: "cuda"      # Force GPU
  # device: "cpu"       # Force CPU
  # device: "mps"       # Apple Silicon GPU
  
  device_map: "auto"    # For multi-GPU setups
  torch_dtype: "auto"   # Automatic precision
  # torch_dtype: "float16"  # Half precision (faster, less memory)
  # torch_dtype: "float32"  # Full precision (slower, more memory)
```

### Memory Optimization

```yaml
model:
  # Quantization options (reduces memory usage)
  load_in_8bit: false   # 8-bit quantization
  load_in_4bit: false   # 4-bit quantization (most memory efficient)
  
  # Cache directory
  cache_dir: null       # Use default HF cache
  # cache_dir: "/path/to/custom/cache"
```

## Generation Parameters

### Core Parameters

```yaml
generation:
  max_new_tokens: 512   # Maximum tokens to generate
  temperature: 0.1      # Randomness (0.0 = deterministic, 1.0+ = creative)
  top_p: 0.95          # Nucleus sampling threshold
  top_k: 50            # Top-k sampling limit
  repetition_penalty: 1.1  # Penalty for repetition
```

### Sampling Control

```yaml
generation:
  do_sample: true       # Enable sampling (vs greedy)
  num_beams: 1         # Beam search (1 = no beam search)
  early_stopping: false # Stop early in beam search
  length_penalty: 1.0   # Length penalty for beam search
```

### Stopping Criteria

```yaml
generation:
  stop_sequences: []    # Custom stop sequences
  # stop_sequences: ["```", "END", "\n\n\n"]
  
  pad_token_id: null    # Padding token (auto-detected)
  eos_token_id: null    # End-of-sequence token (auto-detected)
```

## Prompt Templates

### System Prompt

```yaml
prompts:
  system_prompt: |
    You are an expert programmer with deep knowledge of software development
    best practices. You write clean, efficient, and well-documented code.
```

### Task-Specific Templates

```yaml
prompts:
  code_completion_template: |
    {system_prompt}
    
    Complete the following code:
    ```{language}
    {code}
    ```
    
    Completed code:
  
  code_generation_template: |
    {system_prompt}
    
    Write {language} code for: {description}
    
    Requirements: {requirements}
    
    Code:
```

### Custom Templates

You can add your own templates:

```yaml
prompts:
  code_review_template: |
    {system_prompt}
    
    Review this code and suggest improvements:
    ```{language}
    {code}
    ```
    
    Code review:
  
  optimization_template: |
    {system_prompt}
    
    Optimize the following code for performance:
    ```{language}
    {code}
    ```
    
    Optimized code:
```

## Formatting Options

### Output Format

```yaml
formatting:
  code_block_style: "markdown"    # "markdown", "plain", "highlighted"
  response_format: "structured"   # "structured", "plain", "json"
  include_explanations: true      # Include explanations with code
  include_comments: true          # Include comments in generated code
  strip_extra_whitespace: true    # Clean up whitespace
```

### Language Detection

```yaml
formatting:
  auto_detect_language: true      # Auto-detect programming language
  default_language: "python"     # Default when detection fails
```

## Performance Settings

### Caching

```yaml
performance:
  enable_cache: true              # Enable result caching
  cache_size: 100                # Number of results to cache
```

### Memory Management

```yaml
performance:
  batch_size: 1                   # Batch size for processing
  max_memory_usage: "auto"        # Memory limit
  cleanup_after_generation: true # Clean up after each generation
```

## Safety and Filtering

### Content Filtering

```yaml
safety:
  filter_harmful_content: true    # Filter harmful content
  filter_personal_info: true     # Filter personal information
  warn_dangerous_code: true      # Warn about dangerous code
  block_system_commands: true    # Block system commands
```

### Rate Limiting

```yaml
safety:
  max_requests_per_minute: 60     # Rate limit
  max_tokens_per_request: 2048   # Token limit per request
```

## Modes

Modes allow you to define different configurations for different use cases:

```yaml
modes:
  # Conservative mode for production code
  production:
    temperature: 0.05
    max_new_tokens: 512
    include_explanations: true
    
  # Creative mode for experimental code
  creative:
    temperature: 0.8
    max_new_tokens: 1024
    top_p: 0.95
    
  # Fast mode for quick prototyping
  fast:
    temperature: 0.1
    max_new_tokens: 256
    include_explanations: false
    
  # Debug mode with detailed logging
  debug:
    temperature: 0.1
    max_new_tokens: 512
    log_generation_details: true
    log_timing: true
```

## Language Mappings

Define file extensions for language detection:

```yaml
languages:
  python: [".py", ".pyx", ".pyi"]
  javascript: [".js", ".jsx", ".mjs"]
  typescript: [".ts", ".tsx"]
  java: [".java"]
  cpp: [".cpp", ".cc", ".cxx", ".hpp"]
  go: [".go"]
  rust: [".rs"]
```

## Logging Configuration

```yaml
logging:
  level: "INFO"                   # "DEBUG", "INFO", "WARNING", "ERROR"
  log_file: null                  # Log to file (null = console only)
  log_generation_details: false  # Log detailed generation info
  log_timing: true               # Log timing information
```

## Example Configurations

### Python Development

```yaml
model:
  name: "codellama/CodeLlama-7b-Python-hf"
  torch_dtype: "float16"

generation:
  temperature: 0.1
  max_new_tokens: 1024

prompts:
  system_prompt: |
    You are a Python expert. Write clean, PEP 8 compliant code
    with type hints and docstrings.

formatting:
  default_language: "python"
  include_explanations: true
```

### Web Development

```yaml
model:
  name: "codellama/CodeLlama-7b-hf"

generation:
  temperature: 0.2
  max_new_tokens: 800

prompts:
  system_prompt: |
    You are a web development expert. Write modern, secure,
    and performant web code.

formatting:
  default_language: "javascript"
```

### Data Science

```yaml
model:
  name: "codellama/CodeLlama-7b-Python-hf"

generation:
  temperature: 0.15
  max_new_tokens: 1536

prompts:
  system_prompt: |
    You are a data scientist. Write efficient code using
    pandas, numpy, and scikit-learn best practices.

modes:
  analysis:
    temperature: 0.1
    max_new_tokens: 1024
  visualization:
    temperature: 0.2
    max_new_tokens: 800
```

## Using Configurations

### Command Line

```bash
# Use default configuration
python scripts/cli.py generate "Create a sorting function"

# Use custom configuration
python scripts/cli.py --config my_config.yaml generate "Create a sorting function"

# Use specific mode
python scripts/cli.py --mode creative generate "Create a fun game"
```

### Python API

```python
from codellama_wrapper import CodeLlamaWrapper

# Default configuration
wrapper = CodeLlamaWrapper()

# Custom configuration file
wrapper = CodeLlamaWrapper("my_config.yaml")

# Specific mode
wrapper = CodeLlamaWrapper(mode="creative")

# Custom config file and mode
wrapper = CodeLlamaWrapper("my_config.yaml", mode="production")
```

### Runtime Overrides

```python
# Override parameters at runtime
result = wrapper.generate_code(
    "Create a function",
    temperature=0.5,        # Override temperature
    max_new_tokens=256      # Override max tokens
)
```

## Configuration Validation

The system automatically validates configurations against a JSON schema. Common validation errors:

- **Invalid temperature**: Must be between 0.0 and 2.0
- **Invalid max_tokens**: Must be positive integer
- **Invalid device**: Must be "auto", "cpu", "cuda", or "mps"
- **Missing required fields**: Model name is required

## Best Practices

1. **Start with examples**: Use configurations in `config/examples/` as starting points
2. **Test incrementally**: Make small changes and test them
3. **Use modes**: Define modes for different use cases rather than multiple config files
4. **Monitor performance**: Use logging to track generation times and quality
5. **Version control**: Keep your configurations in version control
6. **Document changes**: Comment your custom configurations

## Troubleshooting

### Configuration not loading
- Check YAML syntax with a validator
- Ensure file path is correct
- Check file permissions

### Validation errors
- Review the error message carefully
- Check parameter ranges and types
- Refer to the schema in `config/schemas/config_schema.json`

### Performance issues
- Reduce `max_new_tokens`
- Lower `temperature` for faster generation
- Enable caching
- Use quantization for memory efficiency

## Next Steps

- **Usage Guide**: [usage.md](usage.md) - Learn how to use the configured system
- **Customization Guide**: [customization.md](customization.md) - Advanced customization techniques
- **Examples**: Check `examples/` directory for practical configuration examples
