# Usage Guide

This guide covers how to use the CodeLlama setup for various code generation tasks.

## Quick Start

### Interactive Chat

The easiest way to get started is with the interactive chat interface:

```bash
python scripts/interactive_chat.py
```

This provides a conversational interface where you can:
- Ask coding questions
- Request code generation
- Get code explanations
- Debug code issues

### Command Line Interface

For scripted usage, use the CLI:

```bash
# Generate code
python scripts/cli.py generate "Create a function to sort a list"

# Complete partial code
python scripts/cli.py complete "def fibonacci(n):"

# Explain existing code
python scripts/cli.py explain "lambda x: x**2"

# Process a file
python scripts/cli.py process-file my_code.py --task explain
```

### Python API

For programmatic usage:

```python
from codellama_wrapper import CodeLlamaWrapper

# Initialize
wrapper = CodeLlamaWrapper()

# Generate code
result = wrapper.generate_code("Create a calculator class")
print(result.text)
```

## Core Functions

### 1. Code Generation

Generate new code from natural language descriptions.

#### Interactive Chat
```
You: Create a function to calculate fibonacci numbers
```

#### CLI
```bash
python scripts/cli.py generate "Create a function to calculate fibonacci numbers" --language python
```

#### Python API
```python
result = wrapper.generate_code(
    description="Create a function to calculate fibonacci numbers",
    language="python",
    requirements="Use recursion and include error handling"
)
```

### 2. Code Completion

Complete partial or incomplete code.

#### Interactive Chat
```
You: /complete def fibonacci(n):
    if n <= 1:
        return n
    # Complete this
```

#### CLI
```bash
python scripts/cli.py complete "def fibonacci(n):\n    if n <= 1:\n        return n\n    # Complete this"
```

#### Python API
```python
partial_code = """
def fibonacci(n):
    if n <= 1:
        return n
    # Complete this
"""
result = wrapper.complete_code(partial_code, language="python")
```

### 3. Code Explanation

Get explanations for existing code.

#### Interactive Chat
```
You: /explain def quicksort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quicksort(left) + middle + quicksort(right)
```

#### CLI
```bash
python scripts/cli.py explain "def quicksort(arr): ..." --language python
```

#### Python API
```python
code = """
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quicksort(left) + middle + quicksort(right)
"""
result = wrapper.explain_code(code, language="python")
```

### 4. Code Debugging

Get help debugging code issues.

#### Interactive Chat
```
You: /debug def divide(a, b):
    return a / b

Error: ZeroDivisionError when b is 0
```

#### CLI
```bash
python scripts/cli.py process-file buggy_code.py --task debug --error "ZeroDivisionError"
```

#### Python API
```python
buggy_code = "def divide(a, b): return a / b"
error_msg = "ZeroDivisionError when b is 0"
result = wrapper.debug_code(buggy_code, error_msg, language="python")
```

## Working with Files

### Process Single Files

```bash
# Explain a Python file
python scripts/cli.py process-file my_script.py --task explain

# Complete code in a file
python scripts/cli.py process-file incomplete.py --task complete

# Debug a file with known error
python scripts/cli.py process-file buggy.py --task debug --error "IndexError"
```

### Batch Processing

Process multiple files at once:

```bash
# Process all Python files in a directory
python scripts/cli.py batch input_dir/ output_dir/ --task explain --pattern "*.py"

# Process with custom configuration
python scripts/cli.py --config my_config.yaml batch input_dir/ output_dir/ --task complete
```

### Batch Processing with JSON

Create a JSON file with tasks:

```json
[
    {
        "id": "task1",
        "type": "generate",
        "prompt": "Create a function to validate email addresses",
        "language": "python"
    },
    {
        "id": "task2",
        "type": "explain",
        "code": "def factorial(n): return 1 if n <= 1 else n * factorial(n-1)",
        "language": "python"
    }
]
```

Then process:

```bash
python scripts/batch_process.py tasks.json --output results.json
```

## Configuration and Modes

### Using Different Configurations

```bash
# Use a specific configuration file
python scripts/cli.py --config config/examples/python_focused.yaml generate "Create a class"

# Use a specific mode
python scripts/cli.py --mode creative generate "Create a fun game"

# Combine configuration and mode
python scripts/cli.py --config my_config.yaml --mode production generate "Create a secure login function"
```

### Available Modes

- **default**: Balanced settings for general use
- **creative**: Higher temperature for more diverse outputs
- **precise**: Deterministic outputs with low temperature
- **debug**: Detailed logging and debugging information

### Runtime Parameter Overrides

```python
# Override specific parameters
result = wrapper.generate_code(
    "Create a sorting function",
    temperature=0.5,        # Override temperature
    max_new_tokens=256,     # Override max tokens
    top_p=0.9              # Override top_p
)
```

## Interactive Chat Commands

The interactive chat supports various commands:

### Basic Commands
- `/help` - Show help message
- `/quit` or `/exit` - Exit the chat
- `/clear` - Clear session history

### Generation Commands
- `/generate <description>` - Generate code from description
- `/complete <code>` - Complete partial code
- `/explain <code>` - Explain code functionality
- `/debug <code> <error>` - Debug code with error message

### Configuration Commands
- `/mode <mode_name>` - Switch to different mode
- `/modes` - List available modes
- `/config` - Show current configuration
- `/stats` - Show generation statistics

### File Commands
- `/save <filename>` - Save last response to file
- `/load <filename>` - Load code from file

## Advanced Usage Patterns

### Workflow Integration

```python
# Multi-step code development workflow
wrapper = CodeLlamaWrapper()

# Step 1: Generate initial code
initial = wrapper.generate_code("Create a todo list class")

# Step 2: Add features
enhanced = wrapper.generate(f"""
Enhance this todo list class with the following features:
- Add priority levels
- Add due dates
- Add categories

Current code:
{initial.text}

Enhanced code:
""")

# Step 3: Add tests
tests = wrapper.generate(f"""
Create comprehensive unit tests for this todo list class:

{enhanced.text}

Unit tests:
""")
```

### Custom Prompt Templates

```python
# Create custom prompts for specific tasks
code_review_prompt = """
Please review the following code and provide feedback on:
1. Code quality and best practices
2. Performance considerations
3. Security issues
4. Maintainability

Code to review:
```{language}
{code}
```

Code review:
"""

# Use custom prompt
review_result = wrapper.generate(
    code_review_prompt.format(
        language="python",
        code=my_code
    )
)
```

### Performance Monitoring

```python
# Monitor generation performance
wrapper = CodeLlamaWrapper()

# Generate multiple pieces of code
for i in range(10):
    result = wrapper.generate_code(f"Create function #{i}")
    print(f"Generation {i}: {result.token_count} tokens in {result.generation_time:.2f}s")

# Get overall statistics
stats = wrapper.get_stats()
print(f"Total: {stats['total_generations']} generations")
print(f"Average time: {stats['total_time'] / stats['total_generations']:.2f}s")
print(f"Total tokens: {stats['total_tokens']}")
```

## Output Formats

### Structured Output

By default, responses include explanations and context:

```
Generated Python Code:

```python
def fibonacci(n):
    """Calculate the nth Fibonacci number using recursion."""
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)
```

This function calculates Fibonacci numbers using a simple recursive approach...
```

### Plain Output

For just the code without explanations:

```yaml
# In configuration
formatting:
  include_explanations: false
  response_format: "plain"
```

### JSON Output

For programmatic processing:

```yaml
# In configuration
formatting:
  response_format: "json"
```

## Error Handling

### Common Errors and Solutions

#### Generation Timeout
```python
try:
    result = wrapper.generate_code("Complex algorithm")
except TimeoutError:
    # Retry with simpler parameters
    result = wrapper.generate_code(
        "Simple algorithm",
        max_new_tokens=256,
        temperature=0.1
    )
```

#### Memory Issues
```python
try:
    result = wrapper.generate_code("Large codebase")
except RuntimeError as e:
    if "out of memory" in str(e):
        # Clear cache and retry
        wrapper.clear_cache()
        result = wrapper.generate_code(
            "Large codebase",
            max_new_tokens=256
        )
```

#### Invalid Code Generation
```python
from utils import validate_code_syntax, extract_code_from_response

result = wrapper.generate_code("Create a function")
code_blocks = extract_code_from_response(result.text)

for language, code in code_blocks:
    is_valid, error = validate_code_syntax(code, language)
    if not is_valid:
        print(f"Invalid code: {error}")
        # Retry or fix the code
```

## Best Practices

### 1. Be Specific in Prompts
```python
# Good
result = wrapper.generate_code(
    "Create a Python function that validates email addresses using regex, "
    "returns True for valid emails and False for invalid ones, "
    "and includes proper error handling for None inputs"
)

# Less effective
result = wrapper.generate_code("Create email validator")
```

### 2. Use Appropriate Modes
```python
# For production code
wrapper = CodeLlamaWrapper(mode="precise")

# For creative/experimental code
wrapper = CodeLlamaWrapper(mode="creative")

# For debugging
wrapper = CodeLlamaWrapper(mode="debug")
```

### 3. Leverage Context
```python
# Provide context for better results
context = "I'm building a web API using Flask"
result = wrapper.generate_code(
    f"{context}. Create a route handler for user authentication",
    requirements="Use JWT tokens and include error handling"
)
```

### 4. Validate Generated Code
```python
# Always validate critical code
result = wrapper.generate_code("Create a security function")
code_blocks = extract_code_from_response(result.text)

for lang, code in code_blocks:
    is_valid, error = validate_code_syntax(code, lang)
    if is_valid:
        print("Code is syntactically valid")
    else:
        print(f"Syntax error: {error}")
```

## Tips and Tricks

1. **Use examples**: Include examples in your prompts for better results
2. **Iterate**: Refine prompts based on initial results
3. **Cache results**: Enable caching for repeated similar requests
4. **Monitor performance**: Track generation times and adjust parameters
5. **Use appropriate models**: Choose model size based on your needs and resources
6. **Combine approaches**: Use multiple generation methods for complex tasks

## Next Steps

- **Customization Guide**: [customization.md](customization.md) - Learn advanced customization techniques
- **Examples**: Explore the `examples/` directory for more usage patterns
- **Configuration**: Review [configuration.md](configuration.md) for detailed configuration options
