#!/usr/bin/env python3
"""
Integration test cases for the complete CodeLlama setup.
"""

import unittest
import tempfile
import subprocess
import sys
import json
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from codellama_wrapper import CodeLlamaWrapper
from config_manager import ConfigManager, ConfigurationError
from utils import (
    extract_code_from_response, validate_code_syntax,
    detect_file_language, format_generation_time
)


class TestEndToEndWorkflow(unittest.TestCase):
    """Test complete end-to-end workflows."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.test_files_dir = Path(self.temp_dir) / "test_files"
        self.test_files_dir.mkdir()
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_configuration_workflow(self):
        """Test complete configuration workflow."""
        # Create config manager
        config_manager = ConfigManager()
        
        # Load default config
        default_config = config_manager.load_default_config()
        self.assertIsInstance(default_config, dict)
        
        # Create custom config
        custom_config = {
            "model": {
                "name": "test-model",
                "device": "cpu"
            },
            "generation": {
                "temperature": 0.2,
                "max_new_tokens": 256
            }
        }
        
        # Save custom config
        custom_path = Path(self.temp_dir) / "custom.yaml"
        config_manager.save_config(custom_config, custom_path)
        
        # Load and validate custom config
        loaded_config = config_manager.load_config(custom_path)
        self.assertEqual(loaded_config["generation"]["temperature"], 0.2)
        
        # Test mode configuration
        if "modes" in loaded_config:
            modes = config_manager.list_available_modes(loaded_config)
            self.assertIsInstance(modes, list)
    
    def test_file_processing_workflow(self):
        """Test file processing workflow."""
        # Create test Python file
        test_code = '''
def fibonacci(n):
    """Calculate fibonacci number."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def main():
    print(fibonacci(10))

if __name__ == "__main__":
    main()
'''
        
        test_file = self.test_files_dir / "test_fibonacci.py"
        with open(test_file, 'w') as f:
            f.write(test_code)
        
        # Test language detection
        from utils import load_file_content
        content, language = load_file_content(test_file)
        
        self.assertEqual(content.strip(), test_code.strip())
        self.assertEqual(language, "python")
        
        # Test code extraction and validation
        code_blocks = extract_code_from_response(f"```python\n{test_code}\n```")
        self.assertEqual(len(code_blocks), 1)
        
        lang, code = code_blocks[0]
        is_valid, error = validate_code_syntax(code, "python")
        self.assertTrue(is_valid, f"Code validation failed: {error}")
    
    def test_batch_processing_workflow(self):
        """Test batch processing workflow."""
        # Create batch task file
        tasks = [
            {
                "id": "task1",
                "type": "generate",
                "prompt": "Create a simple hello world function",
                "language": "python"
            },
            {
                "id": "task2",
                "type": "explain",
                "code": "def greet(name): return f'Hello, {name}!'",
                "language": "python"
            }
        ]
        
        batch_file = Path(self.temp_dir) / "batch_tasks.json"
        with open(batch_file, 'w') as f:
            json.dump(tasks, f)
        
        # Verify batch file structure
        with open(batch_file, 'r') as f:
            loaded_tasks = json.load(f)
        
        self.assertEqual(len(loaded_tasks), 2)
        self.assertEqual(loaded_tasks[0]["type"], "generate")
        self.assertEqual(loaded_tasks[1]["type"], "explain")
    
    def test_utility_functions(self):
        """Test utility functions integration."""
        # Test format_generation_time
        self.assertEqual(format_generation_time(0.5), "500ms")
        self.assertEqual(format_generation_time(1.5), "1.5s")
        self.assertEqual(format_generation_time(65), "1m 5.0s")
        
        # Test detect_file_language
        self.assertEqual(detect_file_language("test.py"), "python")
        self.assertEqual(detect_file_language("test.js"), "javascript")
        self.assertEqual(detect_file_language("test.unknown"), "text")
        
        # Test code extraction
        markdown_text = """
Here's some code:

```python
def hello():
    print("Hello, World!")
```

And some more text.
"""
        
        code_blocks = extract_code_from_response(markdown_text)
        self.assertEqual(len(code_blocks), 1)
        self.assertEqual(code_blocks[0][0], "python")
        self.assertIn("def hello", code_blocks[0][1])


class TestCLIIntegration(unittest.TestCase):
    """Test CLI script integration."""
    
    def setUp(self):
        """Set up test environment."""
        self.scripts_dir = Path(__file__).parent.parent / "scripts"
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_cli_help(self):
        """Test CLI help functionality."""
        if not (self.scripts_dir / "cli.py").exists():
            self.skipTest("CLI script not found")
        
        try:
            result = subprocess.run(
                [sys.executable, str(self.scripts_dir / "cli.py"), "--help"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            self.assertEqual(result.returncode, 0)
            self.assertIn("CodeLlama Command Line Interface", result.stdout)
            
        except subprocess.TimeoutExpired:
            self.skipTest("CLI help command timed out")
        except FileNotFoundError:
            self.skipTest("Python interpreter or CLI script not found")
    
    def test_cli_info_command(self):
        """Test CLI info command."""
        if not (self.scripts_dir / "cli.py").exists():
            self.skipTest("CLI script not found")
        
        try:
            result = subprocess.run(
                [sys.executable, str(self.scripts_dir / "cli.py"), "info"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            # Command should run without error (even if model not available)
            self.assertIn(result.returncode, [0, 1])  # 0 = success, 1 = expected error
            
            # Should contain some system information
            output = result.stdout + result.stderr
            self.assertTrue(
                any(keyword in output.lower() for keyword in 
                    ["system", "python", "configuration", "error"])
            )
            
        except subprocess.TimeoutExpired:
            self.skipTest("CLI info command timed out")
        except FileNotFoundError:
            self.skipTest("Python interpreter or CLI script not found")
    
    def test_interactive_chat_startup(self):
        """Test interactive chat startup."""
        if not (self.scripts_dir / "interactive_chat.py").exists():
            self.skipTest("Interactive chat script not found")
        
        try:
            # Test that the script can at least start and show help
            process = subprocess.Popen(
                [sys.executable, str(self.scripts_dir / "interactive_chat.py")],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Send quit command immediately
            stdout, stderr = process.communicate(input="/quit\n", timeout=30)
            
            # Should exit cleanly
            self.assertEqual(process.returncode, 0)
            
            # Should show welcome message
            output = stdout + stderr
            self.assertTrue(
                any(keyword in output.lower() for keyword in 
                    ["welcome", "codellama", "interactive", "chat"])
            )
            
        except subprocess.TimeoutExpired:
            process.kill()
            self.skipTest("Interactive chat startup timed out")
        except FileNotFoundError:
            self.skipTest("Python interpreter or interactive chat script not found")


class TestConfigurationIntegration(unittest.TestCase):
    """Test configuration system integration."""
    
    def test_example_configurations(self):
        """Test loading and validating example configurations."""
        config_dir = Path(__file__).parent.parent / "config"
        examples_dir = config_dir / "examples"
        
        if not examples_dir.exists():
            self.skipTest("Example configurations not found")
        
        config_manager = ConfigManager()
        
        for config_file in examples_dir.glob("*.yaml"):
            with self.subTest(config_file=config_file.name):
                try:
                    # Load configuration
                    config = config_manager.load_config(config_file)
                    
                    # Basic structure validation
                    self.assertIn("model", config)
                    self.assertIn("generation", config)
                    
                    # Validate against schema
                    config_manager.validate_config(config)
                    
                    # Test mode configurations if present
                    if "modes" in config:
                        modes = config_manager.list_available_modes(config)
                        for mode in modes:
                            mode_config = config_manager.get_mode_config(config, mode)
                            self.assertIsInstance(mode_config, dict)
                    
                except Exception as e:
                    self.fail(f"Configuration {config_file.name} failed: {e}")
    
    def test_configuration_inheritance(self):
        """Test configuration inheritance and merging."""
        config_manager = ConfigManager()
        
        try:
            # Load default configuration
            default_config = config_manager.load_default_config()
            
            # Create a minimal override
            override = {
                "generation": {
                    "temperature": 0.5
                }
            }
            
            # Test merging
            merged = config_manager._deep_merge(default_config, override)
            
            # Should preserve original structure
            self.assertIn("model", merged)
            self.assertIn("generation", merged)
            
            # Should apply override
            self.assertEqual(merged["generation"]["temperature"], 0.5)
            
            # Should preserve other values
            self.assertIn("max_new_tokens", merged["generation"])
            
        except FileNotFoundError:
            self.skipTest("Default configuration not found")


class TestErrorHandling(unittest.TestCase):
    """Test error handling and recovery."""
    
    def test_invalid_configuration_handling(self):
        """Test handling of invalid configurations."""
        config_manager = ConfigManager()
        
        # Test invalid configuration
        invalid_config = {
            "model": {
                # Missing required 'name' field
                "device": "cpu"
            },
            "generation": {
                "temperature": 5.0  # Invalid value (too high)
            }
        }
        
        with self.assertRaises(Exception):
            config_manager.validate_config(invalid_config)
    
    def test_missing_file_handling(self):
        """Test handling of missing files."""
        config_manager = ConfigManager()
        
        # Test loading non-existent file
        with self.assertRaises(Exception):
            config_manager.load_config("nonexistent_config.yaml")
    
    def test_wrapper_error_handling(self):
        """Test wrapper error handling."""
        # Test with invalid configuration
        try:
            wrapper = CodeLlamaWrapper("nonexistent_config.yaml")
            # Should handle gracefully or raise appropriate exception
        except Exception as e:
            # Should be a meaningful error
            self.assertIsInstance(e, (FileNotFoundError, ValueError, RuntimeError, ConfigurationError))


class TestPerformanceAndScaling(unittest.TestCase):
    """Test performance and scaling aspects."""
    
    def test_configuration_loading_performance(self):
        """Test configuration loading performance."""
        import time
        
        config_manager = ConfigManager()
        
        try:
            # Time configuration loading
            start_time = time.time()
            config = config_manager.load_default_config()
            load_time = time.time() - start_time
            
            # Should load quickly (under 1 second)
            self.assertLess(load_time, 1.0)
            
            # Should be valid
            self.assertIsInstance(config, dict)
            
        except FileNotFoundError:
            self.skipTest("Default configuration not found")
    
    def test_memory_usage(self):
        """Test memory usage patterns."""
        import gc
        import sys
        
        # Get initial memory usage
        initial_objects = len(gc.get_objects())
        
        # Create and destroy wrapper (without loading model)
        try:
            wrapper = CodeLlamaWrapper()
            del wrapper
            gc.collect()
            
            # Memory should not grow significantly
            final_objects = len(gc.get_objects())
            object_growth = final_objects - initial_objects
            
            # Allow some growth but not excessive
            self.assertLess(object_growth, 1000)
            
        except Exception:
            # Skip if wrapper creation fails
            self.skipTest("Wrapper creation failed")


if __name__ == "__main__":
    # Run tests with verbose output
    unittest.main(verbosity=2)
