#!/usr/bin/env python3
"""
Test cases for CodeLlama wrapper functionality.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from codellama_wrapper import CodeLlamaWrapper, GenerationResult, StopSequenceCriteria
from config_manager import ConfigManager


class TestGenerationResult(unittest.TestCase):
    """Test cases for GenerationResult dataclass."""
    
    def test_generation_result_creation(self):
        """Test creating GenerationResult instance."""
        result = GenerationResult(
            text="def hello(): print('Hello')",
            prompt="Create a hello function",
            generation_time=1.5,
            token_count=10,
            model_name="test-model",
            config_used={"temperature": 0.1}
        )
        
        self.assertEqual(result.text, "def hello(): print('Hello')")
        self.assertEqual(result.prompt, "Create a hello function")
        self.assertEqual(result.generation_time, 1.5)
        self.assertEqual(result.token_count, 10)
        self.assertEqual(result.model_name, "test-model")
        self.assertEqual(result.config_used["temperature"], 0.1)


class TestStopSequenceCriteria(unittest.TestCase):
    """Test cases for StopSequenceCriteria class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_tokenizer = Mock()
        self.mock_tokenizer.decode.return_value = "def function():\n    return True\n```"
        
        self.criteria = StopSequenceCriteria(["```", "END"], self.mock_tokenizer)
    
    def test_stop_sequence_detection(self):
        """Test detection of stop sequences."""
        import torch
        
        # Mock input_ids tensor
        input_ids = torch.tensor([[1, 2, 3, 4, 5]])
        scores = torch.tensor([[0.1, 0.2, 0.3]])
        
        # Should return True when stop sequence is found
        result = self.criteria(input_ids, scores)
        self.assertTrue(result)
    
    def test_no_stop_sequence(self):
        """Test when no stop sequence is found."""
        import torch
        
        self.mock_tokenizer.decode.return_value = "def function():\n    return True"
        
        input_ids = torch.tensor([[1, 2, 3, 4, 5]])
        scores = torch.tensor([[0.1, 0.2, 0.3]])
        
        result = self.criteria(input_ids, scores)
        self.assertFalse(result)


class TestCodeLlamaWrapper(unittest.TestCase):
    """Test cases for CodeLlamaWrapper class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary config for testing
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir) / "config"
        self.config_dir.mkdir(parents=True)
        
        # Create minimal test config
        test_config = {
            "model": {
                "name": "test-model",
                "device": "cpu"
            },
            "generation": {
                "max_new_tokens": 100,
                "temperature": 0.1
            },
            "prompts": {
                "system_prompt": "You are a helpful assistant.",
                "code_generation_template": "{system_prompt}\n\nCreate code for: {description}\n\nCode:",
                "code_completion_template": "{system_prompt}\n\nComplete: {code}\n\nCompleted:",
                "code_explanation_template": "{system_prompt}\n\nExplain: {code}\n\nExplanation:",
                "debug_template": "{system_prompt}\n\nDebug: {code}\nError: {error}\n\nSolution:"
            },
            "formatting": {
                "auto_detect_language": True,
                "default_language": "python"
            },
            "performance": {
                "enable_cache": True,
                "cache_size": 10
            },
            "logging": {
                "level": "INFO"
            }
        }
        
        import yaml
        with open(self.config_dir / "test_config.yaml", 'w') as f:
            yaml.dump(test_config, f)
        
        # Mock the config manager to use our test config
        with patch('codellama_wrapper.ConfigManager') as mock_config_manager:
            mock_manager = Mock()
            mock_manager.load_config.return_value = test_config
            mock_manager.get_mode_config.return_value = test_config
            mock_config_manager.return_value = mock_manager
            
            self.wrapper = CodeLlamaWrapper(self.config_dir / "test_config.yaml")
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """Test wrapper initialization."""
        self.assertIsNotNone(self.wrapper.config)
        self.assertIsNotNone(self.wrapper.config_manager)
        self.assertEqual(self.wrapper.config["model"]["name"], "test-model")
    
    def test_language_detection(self):
        """Test programming language detection."""
        # Test Python detection
        python_code = "def hello():\n    print('Hello')"
        detected = self.wrapper._detect_language(python_code)
        self.assertEqual(detected, "python")
        
        # Test JavaScript detection
        js_code = "function hello() { console.log('Hello'); }"
        detected = self.wrapper._detect_language(js_code)
        self.assertEqual(detected, "javascript")
        
        # Test fallback to default
        unknown_code = "some random text"
        detected = self.wrapper._detect_language(unknown_code)
        self.assertEqual(detected, "python")  # Default language
    
    @patch('codellama_wrapper.AutoTokenizer')
    @patch('codellama_wrapper.AutoModelForCausalLM')
    def test_model_loading(self, mock_model_class, mock_tokenizer_class):
        """Test model loading functionality."""
        # Mock tokenizer
        mock_tokenizer = Mock()
        mock_tokenizer.pad_token = None
        mock_tokenizer.eos_token = "<eos>"
        mock_tokenizer.pad_token_id = 0
        mock_tokenizer.eos_token_id = 1
        mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
        
        # Mock model
        mock_model = Mock()
        mock_model_class.from_pretrained.return_value = mock_model
        
        # Test loading
        self.wrapper.load_model()
        
        # Verify model and tokenizer were loaded
        self.assertIsNotNone(self.wrapper.model)
        self.assertIsNotNone(self.wrapper.tokenizer)
        self.assertIsNotNone(self.wrapper.generation_config)
        
        # Verify tokenizer configuration
        mock_tokenizer_class.from_pretrained.assert_called_once()
        mock_model_class.from_pretrained.assert_called_once()
    
    @patch('codellama_wrapper.torch')
    def test_generate_with_mocked_model(self, mock_torch):
        """Test generation with mocked model."""
        # Mock torch and model components
        mock_torch.cuda.is_available.return_value = False
        mock_torch.no_grad.return_value.__enter__ = Mock()
        mock_torch.no_grad.return_value.__exit__ = Mock()
        
        # Mock model and tokenizer
        self.wrapper.model = Mock()
        self.wrapper.tokenizer = Mock()
        self.wrapper.generation_config = Mock()
        self.wrapper.generation_config.to_dict.return_value = {"temperature": 0.1}
        
        # Mock tokenizer behavior
        self.wrapper.tokenizer.return_value = {
            "input_ids": mock_torch.tensor([[1, 2, 3]]),
            "attention_mask": mock_torch.tensor([[1, 1, 1]])
        }
        self.wrapper.tokenizer.decode.return_value = "Generated code here"
        
        # Mock model generation
        mock_outputs = Mock()
        mock_outputs.sequences = [mock_torch.tensor([1, 2, 3, 4, 5, 6])]
        self.wrapper.model.generate.return_value = mock_outputs
        self.wrapper.model.device.type = "cpu"
        
        # Test generation
        result = self.wrapper.generate("Test prompt")
        
        # Verify result
        self.assertIsInstance(result, GenerationResult)
        self.assertEqual(result.text, "Generated code here")
        self.assertEqual(result.prompt, "Test prompt")
    
    def test_code_generation_methods(self):
        """Test high-level code generation methods."""
        # Mock the generate method
        mock_result = GenerationResult(
            text="def test(): pass",
            prompt="test prompt",
            generation_time=1.0,
            token_count=5,
            model_name="test-model",
            config_used={}
        )
        
        with patch.object(self.wrapper, 'generate', return_value=mock_result):
            # Test generate_code
            result = self.wrapper.generate_code("Create a test function")
            self.assertIsInstance(result, GenerationResult)
            
            # Test complete_code
            result = self.wrapper.complete_code("def test():")
            self.assertIsInstance(result, GenerationResult)
            
            # Test explain_code
            result = self.wrapper.explain_code("def test(): pass")
            self.assertIsInstance(result, GenerationResult)
            
            # Test debug_code
            result = self.wrapper.debug_code("def test(): pass", "SyntaxError")
            self.assertIsInstance(result, GenerationResult)
    
    def test_caching(self):
        """Test caching functionality."""
        # Enable caching
        self.wrapper.config["performance"]["enable_cache"] = True
        self.wrapper.config["performance"]["cache_size"] = 2
        
        # Mock generate method to track calls
        original_generate = self.wrapper.generate
        call_count = 0
        
        def mock_generate(prompt, **kwargs):
            nonlocal call_count
            call_count += 1
            return GenerationResult(
                text=f"Result {call_count}",
                prompt=prompt,
                generation_time=1.0,
                token_count=5,
                model_name="test",
                config_used={}
            )
        
        with patch.object(self.wrapper, 'generate', side_effect=mock_generate):
            # First call - should generate
            result1 = self.wrapper.generate("test prompt")
            self.assertEqual(call_count, 1)
            
            # Second call with same prompt - should use cache
            result2 = self.wrapper.generate("test prompt")
            self.assertEqual(call_count, 1)  # No additional call
            
            # Different prompt - should generate
            result3 = self.wrapper.generate("different prompt")
            self.assertEqual(call_count, 2)
    
    def test_stats_tracking(self):
        """Test statistics tracking."""
        initial_stats = self.wrapper.get_stats()
        self.assertEqual(initial_stats["total_generations"], 0)
        
        # Mock a generation
        mock_result = GenerationResult(
            text="test",
            prompt="test",
            generation_time=1.5,
            token_count=10,
            model_name="test",
            config_used={}
        )
        
        # Simulate updating stats
        self.wrapper.stats["total_generations"] += 1
        self.wrapper.stats["total_tokens"] += 10
        self.wrapper.stats["total_time"] += 1.5
        
        updated_stats = self.wrapper.get_stats()
        self.assertEqual(updated_stats["total_generations"], 1)
        self.assertEqual(updated_stats["total_tokens"], 10)
        self.assertEqual(updated_stats["total_time"], 1.5)
    
    def test_cache_management(self):
        """Test cache management operations."""
        # Add some items to cache
        self.wrapper.generation_cache = {
            "key1": "value1",
            "key2": "value2"
        }
        
        # Test cache clearing
        self.wrapper.clear_cache()
        self.assertEqual(len(self.wrapper.generation_cache), 0)
    
    def test_model_unloading(self):
        """Test model unloading."""
        # Set up mock model and tokenizer
        self.wrapper.model = Mock()
        self.wrapper.tokenizer = Mock()
        
        # Test unloading
        with patch('codellama_wrapper.torch') as mock_torch:
            mock_torch.cuda.is_available.return_value = True
            mock_torch.cuda.empty_cache = Mock()
            
            self.wrapper.unload_model()
            
            self.assertIsNone(self.wrapper.model)
            self.assertIsNone(self.wrapper.tokenizer)
            mock_torch.cuda.empty_cache.assert_called_once()


class TestWrapperIntegration(unittest.TestCase):
    """Integration tests for wrapper functionality."""
    
    def test_wrapper_with_real_config(self):
        """Test wrapper with real configuration files."""
        try:
            # Try to create wrapper with default config
            wrapper = CodeLlamaWrapper()
            
            # Basic functionality tests (without loading actual model)
            self.assertIsNotNone(wrapper.config)
            self.assertIn("model", wrapper.config)
            self.assertIn("generation", wrapper.config)
            
            # Test language detection
            python_code = "def test(): pass"
            detected = wrapper._detect_language(python_code)
            self.assertIsInstance(detected, str)
            
        except Exception as e:
            self.skipTest(f"Real configuration not available: {e}")
    
    def test_prompt_template_formatting(self):
        """Test prompt template formatting."""
        wrapper = CodeLlamaWrapper()
        
        # Test that templates exist and can be formatted
        templates = wrapper.config.get("prompts", {})
        
        if "code_generation_template" in templates:
            template = templates["code_generation_template"]
            
            # Test formatting
            formatted = template.format(
                system_prompt="Test system prompt",
                description="Test description",
                language="python",
                requirements="Test requirements"
            )
            
            self.assertIn("Test system prompt", formatted)
            self.assertIn("Test description", formatted)


if __name__ == "__main__":
    unittest.main()
