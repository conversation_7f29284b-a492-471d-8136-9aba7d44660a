#!/usr/bin/env python3
"""
Test cases for configuration management functionality.
"""

import unittest
import tempfile
import yaml
import json
from pathlib import Path
import sys

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config_manager import ConfigManager, ConfigurationError


class TestConfigManager(unittest.TestCase):
    """Test cases for ConfigManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir) / "config"
        self.config_dir.mkdir(parents=True)
        
        # Create test schema
        self.schema_dir = self.config_dir / "schemas"
        self.schema_dir.mkdir()
        
        test_schema = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "properties": {
                "model": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "device": {"type": "string", "enum": ["auto", "cpu", "cuda"]}
                    },
                    "required": ["name"]
                },
                "generation": {
                    "type": "object",
                    "properties": {
                        "temperature": {"type": "number", "minimum": 0, "maximum": 2},
                        "max_new_tokens": {"type": "integer", "minimum": 1}
                    }
                }
            },
            "required": ["model"]
        }
        
        with open(self.schema_dir / "config_schema.json", 'w') as f:
            json.dump(test_schema, f)
        
        # Create test default config
        test_config = {
            "model": {
                "name": "test-model",
                "device": "auto"
            },
            "generation": {
                "temperature": 0.1,
                "max_new_tokens": 512
            }
        }
        
        with open(self.config_dir / "default_config.yaml", 'w') as f:
            yaml.dump(test_config, f)
        
        self.config_manager = ConfigManager(self.config_dir)
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_load_default_config(self):
        """Test loading default configuration."""
        config = self.config_manager.load_default_config()
        
        self.assertIsInstance(config, dict)
        self.assertIn("model", config)
        self.assertIn("generation", config)
        self.assertEqual(config["model"]["name"], "test-model")
    
    def test_load_schema(self):
        """Test loading configuration schema."""
        schema = self.config_manager.load_schema()
        
        self.assertIsInstance(schema, dict)
        self.assertIn("properties", schema)
        self.assertIn("model", schema["properties"])
    
    def test_validate_valid_config(self):
        """Test validation of valid configuration."""
        valid_config = {
            "model": {
                "name": "valid-model",
                "device": "cuda"
            },
            "generation": {
                "temperature": 0.5,
                "max_new_tokens": 256
            }
        }
        
        # Should not raise exception
        self.config_manager.validate_config(valid_config)
    
    def test_validate_invalid_config(self):
        """Test validation of invalid configuration."""
        invalid_config = {
            "model": {
                "device": "invalid-device"  # Missing required 'name'
            },
            "generation": {
                "temperature": 3.0  # Exceeds maximum
            }
        }
        
        with self.assertRaises(ConfigurationError):
            self.config_manager.validate_config(invalid_config)
    
    def test_load_custom_config(self):
        """Test loading custom configuration file."""
        custom_config = {
            "model": {
                "name": "custom-model",
                "device": "cpu"
            },
            "generation": {
                "temperature": 0.3
            }
        }
        
        custom_path = self.config_dir / "custom.yaml"
        with open(custom_path, 'w') as f:
            yaml.dump(custom_config, f)
        
        loaded_config = self.config_manager.load_config(custom_path)
        
        # Should merge with defaults
        self.assertEqual(loaded_config["model"]["name"], "custom-model")
        self.assertEqual(loaded_config["model"]["device"], "cpu")
        self.assertEqual(loaded_config["generation"]["temperature"], 0.3)
        self.assertEqual(loaded_config["generation"]["max_new_tokens"], 512)  # From default
    
    def test_deep_merge(self):
        """Test deep merging of configurations."""
        base = {
            "a": 1,
            "b": {
                "c": 2,
                "d": 3
            }
        }
        
        override = {
            "b": {
                "c": 4,
                "e": 5
            },
            "f": 6
        }
        
        result = self.config_manager._deep_merge(base, override)
        
        self.assertEqual(result["a"], 1)
        self.assertEqual(result["b"]["c"], 4)  # Overridden
        self.assertEqual(result["b"]["d"], 3)  # Preserved
        self.assertEqual(result["b"]["e"], 5)  # Added
        self.assertEqual(result["f"], 6)       # Added
    
    def test_get_mode_config(self):
        """Test getting mode-specific configuration."""
        config = {
            "generation": {
                "temperature": 0.1,
                "max_new_tokens": 512
            },
            "modes": {
                "creative": {
                    "temperature": 0.8,
                    "max_new_tokens": 1024
                }
            }
        }
        
        mode_config = self.config_manager.get_mode_config(config, "creative")
        
        self.assertEqual(mode_config["generation"]["temperature"], 0.8)
        self.assertEqual(mode_config["generation"]["max_new_tokens"], 1024)
    
    def test_list_available_modes(self):
        """Test listing available modes."""
        config = {
            "modes": {
                "default": {},
                "creative": {},
                "precise": {}
            }
        }
        
        modes = self.config_manager.list_available_modes(config)
        
        self.assertIn("default", modes)
        self.assertIn("creative", modes)
        self.assertIn("precise", modes)
    
    def test_save_config(self):
        """Test saving configuration to file."""
        config = {
            "model": {
                "name": "save-test-model"
            },
            "generation": {
                "temperature": 0.2
            }
        }
        
        save_path = self.config_dir / "saved.yaml"
        self.config_manager.save_config(config, save_path)
        
        # Verify file was created and contains correct data
        self.assertTrue(save_path.exists())
        
        with open(save_path, 'r') as f:
            loaded = yaml.safe_load(f)
        
        self.assertEqual(loaded["model"]["name"], "save-test-model")
        self.assertEqual(loaded["generation"]["temperature"], 0.2)
    
    def test_create_example_config(self):
        """Test creating example configuration."""
        example_path = self.config_dir / "example.yaml"
        self.config_manager.create_example_config(example_path)
        
        self.assertTrue(example_path.exists())
        
        with open(example_path, 'r') as f:
            config = yaml.safe_load(f)
        
        self.assertIn("model", config)
        self.assertIn("generation", config)
    
    def test_nonexistent_config_file(self):
        """Test handling of nonexistent configuration file."""
        nonexistent_path = self.config_dir / "nonexistent.yaml"
        
        with self.assertRaises(ConfigurationError):
            self.config_manager.load_config(nonexistent_path)
    
    def test_invalid_yaml_syntax(self):
        """Test handling of invalid YAML syntax."""
        invalid_yaml_path = self.config_dir / "invalid.yaml"
        
        with open(invalid_yaml_path, 'w') as f:
            f.write("invalid: yaml: syntax: [")
        
        with self.assertRaises(ConfigurationError):
            self.config_manager.load_config(invalid_yaml_path)
    
    def test_json_config_support(self):
        """Test support for JSON configuration files."""
        json_config = {
            "model": {
                "name": "json-model"
            },
            "generation": {
                "temperature": 0.4
            }
        }
        
        json_path = self.config_dir / "test.json"
        with open(json_path, 'w') as f:
            json.dump(json_config, f)
        
        loaded_config = self.config_manager.load_config(json_path)
        
        self.assertEqual(loaded_config["model"]["name"], "json-model")
        self.assertEqual(loaded_config["generation"]["temperature"], 0.4)


class TestConfigurationIntegration(unittest.TestCase):
    """Integration tests for configuration system."""
    
    def test_real_config_loading(self):
        """Test loading actual configuration files."""
        # This test uses the real config files if they exist
        try:
            config_manager = ConfigManager()
            config = config_manager.load_default_config()
            
            # Basic structure checks
            self.assertIn("model", config)
            self.assertIn("generation", config)
            self.assertIn("prompts", config)
            
            # Validate against schema
            config_manager.validate_config(config)
            
        except FileNotFoundError:
            # Skip if config files don't exist
            self.skipTest("Default configuration files not found")
    
    def test_example_configs(self):
        """Test loading example configuration files."""
        examples_dir = Path(__file__).parent.parent / "config" / "examples"
        
        if not examples_dir.exists():
            self.skipTest("Example configurations not found")
        
        config_manager = ConfigManager()
        
        for config_file in examples_dir.glob("*.yaml"):
            with self.subTest(config_file=config_file.name):
                try:
                    config = config_manager.load_config(config_file)
                    
                    # Basic validation
                    self.assertIn("model", config)
                    self.assertIn("generation", config)
                    
                    # Schema validation
                    config_manager.validate_config(config)
                    
                except Exception as e:
                    self.fail(f"Failed to load {config_file.name}: {e}")


if __name__ == "__main__":
    unittest.main()
