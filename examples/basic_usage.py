#!/usr/bin/env python3
"""
Basic Usage Examples for CodeLlama Setup

This script demonstrates basic usage patterns for the CodeLlama wrapper
and configuration system.
"""

import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from codellama_wrapper import CodeLlamaWrapper
from config_manager import ConfigMana<PERSON>


def example_basic_generation():
    """Example: Basic code generation"""
    print("=== Basic Code Generation ===")
    
    # Initialize with default configuration
    wrapper = CodeLlamaWrapper()
    
    # Generate code from description
    result = wrapper.generate_code(
        description="Create a function to calculate the factorial of a number",
        language="python"
    )
    
    print("Generated code:")
    print(result.text)
    print(f"\nGeneration stats: {result.token_count} tokens in {result.generation_time:.2f}s")


def example_code_completion():
    """Example: Code completion"""
    print("\n=== Code Completion ===")
    
    wrapper = CodeLlamaWrapper()
    
    partial_code = """
def fibonacci(n):
    if n <= 1:
        return n
    # Complete this function
    """
    
    result = wrapper.complete_code(partial_code, language="python")
    
    print("Completed code:")
    print(result.text)


def example_code_explanation():
    """Example: Code explanation"""
    print("\n=== Code Explanation ===")
    
    wrapper = CodeLlamaWrapper()
    
    code_to_explain = """
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quicksort(left) + middle + quicksort(right)
    """
    
    result = wrapper.explain_code(code_to_explain, language="python")
    
    print("Code explanation:")
    print(result.text)


def example_custom_configuration():
    """Example: Using custom configuration"""
    print("\n=== Custom Configuration ===")
    
    # Create a custom configuration
    config_manager = ConfigManager()
    
    # Load default config and modify it
    config = config_manager.load_default_config()
    
    # Modify generation parameters
    config["generation"]["temperature"] = 0.3
    config["generation"]["max_new_tokens"] = 256
    
    # Save custom config
    custom_config_path = Path("custom_config.yaml")
    config_manager.save_config(config, custom_config_path)
    
    # Use custom configuration
    wrapper = CodeLlamaWrapper(custom_config_path)
    
    result = wrapper.generate_code(
        description="Create a simple calculator class",
        language="python"
    )
    
    print("Generated with custom config:")
    print(result.text)
    
    # Clean up
    custom_config_path.unlink()


def example_different_modes():
    """Example: Using different modes"""
    print("\n=== Different Modes ===")
    
    # Creative mode
    print("Creative mode:")
    wrapper_creative = CodeLlamaWrapper(mode="creative")
    result = wrapper_creative.generate_code(
        description="Create a fun greeting function",
        language="python"
    )
    print(result.text[:200] + "...")
    
    # Precise mode
    print("\nPrecise mode:")
    wrapper_precise = CodeLlamaWrapper(mode="precise")
    result = wrapper_precise.generate_code(
        description="Create a fun greeting function", 
        language="python"
    )
    print(result.text[:200] + "...")


def example_debugging():
    """Example: Code debugging"""
    print("\n=== Code Debugging ===")
    
    wrapper = CodeLlamaWrapper()
    
    buggy_code = """
def divide_numbers(a, b):
    return a / b

result = divide_numbers(10, 0)
print(result)
    """
    
    error_message = "ZeroDivisionError: division by zero"
    
    result = wrapper.debug_code(buggy_code, error_message, language="python")
    
    print("Debug assistance:")
    print(result.text)


def example_batch_operations():
    """Example: Multiple operations with same model"""
    print("\n=== Batch Operations ===")
    
    wrapper = CodeLlamaWrapper()
    
    tasks = [
        ("generate", "Create a function to reverse a string", "python"),
        ("generate", "Create a function to check if a number is prime", "python"),
        ("generate", "Create a function to merge two sorted lists", "python")
    ]
    
    results = []
    for task_type, description, language in tasks:
        if task_type == "generate":
            result = wrapper.generate_code(description, language=language)
            results.append(result)
            print(f"Generated: {description[:30]}... ({result.token_count} tokens)")
    
    # Show statistics
    stats = wrapper.get_stats()
    print(f"\nBatch stats: {stats['total_generations']} generations, "
          f"{stats['total_tokens']} tokens, {stats['total_time']:.2f}s total")


def example_configuration_management():
    """Example: Configuration management"""
    print("\n=== Configuration Management ===")
    
    config_manager = ConfigManager()
    
    # List available modes
    config = config_manager.load_default_config()
    modes = config_manager.list_available_modes(config)
    print(f"Available modes: {modes}")
    
    # Get mode-specific configuration
    creative_config = config_manager.get_mode_config(config, "creative")
    print(f"Creative mode temperature: {creative_config['generation']['temperature']}")
    
    # Create example configuration
    example_path = Path("example_config.yaml")
    config_manager.create_example_config(example_path)
    print(f"Created example config: {example_path}")
    
    # Load and validate custom config
    loaded_config = config_manager.load_config(example_path)
    print("Custom config loaded and validated successfully")
    
    # Clean up
    example_path.unlink()


def main():
    """Run all examples"""
    print("CodeLlama Setup - Basic Usage Examples")
    print("=" * 50)
    
    try:
        example_basic_generation()
        example_code_completion()
        example_code_explanation()
        example_custom_configuration()
        example_different_modes()
        example_debugging()
        example_batch_operations()
        example_configuration_management()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure you have the required dependencies installed and a compatible model available.")


if __name__ == "__main__":
    main()
