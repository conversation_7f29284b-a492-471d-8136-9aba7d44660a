#!/usr/bin/env python3
"""
Custom Configuration Examples for CodeLlama Setup

This script demonstrates how to create and use custom configurations
for different use cases and scenarios.
"""

import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from codellama_wrapper import CodeLlamaWrapper
from config_manager import Config<PERSON>ana<PERSON>


def create_javascript_config():
    """Create a configuration optimized for JavaScript development"""
    print("=== Creating JavaScript-focused Configuration ===")
    
    config_manager = ConfigManager()
    base_config = config_manager.load_default_config()
    
    # Customize for JavaScript
    js_config = {
        "model": {
            "name": "codellama/CodeLlama-7b-hf",
            "device": "auto",
            "torch_dtype": "float16"
        },
        "generation": {
            "max_new_tokens": 800,
            "temperature": 0.2,
            "top_p": 0.9
        },
        "prompts": {
            "system_prompt": """You are an expert JavaScript developer with deep knowledge of 
            modern JavaScript, ES6+, Node.js, and web development best practices. You write 
            clean, efficient, and well-documented JavaScript code.""",
            
            "code_generation_template": """{system_prompt}

Write JavaScript code for the following task:
{description}

Requirements:
{requirements}

Please use modern JavaScript features (ES6+) and include:
- Proper error handling
- JSDoc comments where appropriate
- Async/await for asynchronous operations when needed

JavaScript code:"""
        },
        "formatting": {
            "default_language": "javascript",
            "include_explanations": True
        },
        "modes": {
            "frontend": {
                "temperature": 0.25,
                "max_new_tokens": 1024
            },
            "backend": {
                "temperature": 0.15,
                "max_new_tokens": 800
            },
            "react": {
                "temperature": 0.3,
                "max_new_tokens": 1200
            }
        }
    }
    
    # Save the configuration
    js_config_path = Path("config/examples/javascript_focused.yaml")
    js_config_path.parent.mkdir(parents=True, exist_ok=True)
    config_manager.save_config(js_config, js_config_path)
    
    print(f"JavaScript configuration saved to: {js_config_path}")
    
    # Test the configuration
    wrapper = CodeLlamaWrapper(js_config_path, mode="react")
    result = wrapper.generate_code(
        description="Create a React component for a todo list",
        language="javascript"
    )
    
    print("Generated React component:")
    print(result.text[:300] + "...")
    
    return js_config_path


def create_data_science_config():
    """Create a configuration optimized for data science tasks"""
    print("\n=== Creating Data Science Configuration ===")
    
    config_manager = ConfigManager()
    
    ds_config = {
        "model": {
            "name": "codellama/CodeLlama-7b-Python-hf",
            "device": "auto",
            "torch_dtype": "float16"
        },
        "generation": {
            "max_new_tokens": 1536,
            "temperature": 0.1,
            "top_p": 0.85
        },
        "prompts": {
            "system_prompt": """You are a data scientist and Python expert specializing in 
            data analysis, machine learning, and scientific computing. You use libraries like 
            pandas, numpy, scikit-learn, matplotlib, and seaborn effectively. You write 
            well-documented, efficient code with proper data validation.""",
            
            "code_generation_template": """{system_prompt}

Create Python code for the following data science task:
{description}

Requirements:
{requirements}

Include:
- Proper imports for required libraries
- Data validation and error handling
- Clear variable names and comments
- Visualization code where appropriate
- Performance considerations for large datasets

Python code:"""
        },
        "formatting": {
            "default_language": "python",
            "include_explanations": True,
            "include_comments": True
        },
        "modes": {
            "analysis": {
                "temperature": 0.05,
                "max_new_tokens": 1024
            },
            "modeling": {
                "temperature": 0.1,
                "max_new_tokens": 1536
            },
            "visualization": {
                "temperature": 0.15,
                "max_new_tokens": 800
            }
        }
    }
    
    # Save the configuration
    ds_config_path = Path("config/examples/data_science.yaml")
    config_manager.save_config(ds_config, ds_config_path)
    
    print(f"Data science configuration saved to: {ds_config_path}")
    
    # Test the configuration
    wrapper = CodeLlamaWrapper(ds_config_path, mode="analysis")
    result = wrapper.generate_code(
        description="Analyze a dataset of sales data and create visualizations",
        requirements="Use pandas for data manipulation and matplotlib for plotting"
    )
    
    print("Generated data analysis code:")
    print(result.text[:400] + "...")
    
    return ds_config_path


def create_minimal_config():
    """Create a minimal configuration for quick testing"""
    print("\n=== Creating Minimal Configuration ===")
    
    config_manager = ConfigManager()
    
    minimal_config = {
        "model": {
            "name": "codellama/CodeLlama-7b-hf",
            "device": "auto"
        },
        "generation": {
            "max_new_tokens": 256,
            "temperature": 0.1
        },
        "formatting": {
            "include_explanations": False,
            "response_format": "plain"
        },
        "performance": {
            "cleanup_after_generation": True,
            "enable_cache": False
        }
    }
    
    # Save the configuration
    minimal_config_path = Path("config/examples/minimal.yaml")
    config_manager.save_config(minimal_config, minimal_config_path)
    
    print(f"Minimal configuration saved to: {minimal_config_path}")
    
    # Test the configuration
    wrapper = CodeLlamaWrapper(minimal_config_path)
    result = wrapper.generate_code(
        description="Create a simple hello world function",
        language="python"
    )
    
    print("Generated code (minimal config):")
    print(result.text)
    
    return minimal_config_path


def create_security_focused_config():
    """Create a configuration focused on secure coding practices"""
    print("\n=== Creating Security-focused Configuration ===")
    
    config_manager = ConfigManager()
    
    security_config = {
        "model": {
            "name": "codellama/CodeLlama-13b-hf",
            "device": "auto",
            "torch_dtype": "float16"
        },
        "generation": {
            "max_new_tokens": 1024,
            "temperature": 0.05,
            "top_p": 0.8
        },
        "prompts": {
            "system_prompt": """You are a security-conscious software engineer with expertise 
            in secure coding practices. You always consider security implications and follow 
            OWASP guidelines. You implement proper input validation, authentication, 
            authorization, and protection against common vulnerabilities.""",
            
            "code_generation_template": """{system_prompt}

Write secure code for the following task:
{description}

Security requirements:
{requirements}

Ensure the code includes:
- Input validation and sanitization
- Proper error handling without information leakage
- Protection against common vulnerabilities (SQL injection, XSS, etc.)
- Secure authentication and authorization where applicable
- Logging for security events
- Comments explaining security considerations

Secure code:"""
        },
        "safety": {
            "filter_harmful_content": True,
            "filter_personal_info": True,
            "warn_dangerous_code": True,
            "block_system_commands": True,
            "max_requests_per_minute": 20
        },
        "modes": {
            "web_security": {
                "temperature": 0.03,
                "max_new_tokens": 1200
            },
            "crypto": {
                "temperature": 0.01,
                "max_new_tokens": 800
            },
            "audit": {
                "temperature": 0.05,
                "max_new_tokens": 1536
            }
        }
    }
    
    # Save the configuration
    security_config_path = Path("config/examples/security_focused.yaml")
    config_manager.save_config(security_config, security_config_path)
    
    print(f"Security-focused configuration saved to: {security_config_path}")
    
    # Test the configuration
    wrapper = CodeLlamaWrapper(security_config_path, mode="web_security")
    result = wrapper.generate_code(
        description="Create a user authentication system",
        requirements="Protect against common web vulnerabilities"
    )
    
    print("Generated secure authentication code:")
    print(result.text[:400] + "...")
    
    return security_config_path


def demonstrate_config_inheritance():
    """Demonstrate configuration inheritance and merging"""
    print("\n=== Configuration Inheritance Example ===")
    
    config_manager = ConfigManager()
    
    # Create a base configuration
    base_config = {
        "model": {
            "name": "codellama/CodeLlama-7b-hf",
            "device": "auto"
        },
        "generation": {
            "max_new_tokens": 512,
            "temperature": 0.1
        }
    }
    
    # Create an override configuration
    override_config = {
        "generation": {
            "temperature": 0.3,  # Override temperature
            "top_p": 0.9         # Add new parameter
        },
        "formatting": {
            "include_explanations": True  # Add new section
        }
    }
    
    # Merge configurations
    merged_config = config_manager._deep_merge(base_config, override_config)
    
    print("Base config temperature:", base_config["generation"]["temperature"])
    print("Merged config temperature:", merged_config["generation"]["temperature"])
    print("Merged config has formatting:", "formatting" in merged_config)
    print("Merged config max_tokens:", merged_config["generation"]["max_new_tokens"])


def main():
    """Run all custom configuration examples"""
    print("CodeLlama Setup - Custom Configuration Examples")
    print("=" * 60)
    
    try:
        # Create various custom configurations
        js_config = create_javascript_config()
        ds_config = create_data_science_config()
        minimal_config = create_minimal_config()
        security_config = create_security_focused_config()
        
        # Demonstrate configuration concepts
        demonstrate_config_inheritance()
        
        print("\n" + "=" * 60)
        print("Custom configuration examples completed!")
        print("\nCreated configurations:")
        print(f"- JavaScript: {js_config}")
        print(f"- Data Science: {ds_config}")
        print(f"- Minimal: {minimal_config}")
        print(f"- Security: {security_config}")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
