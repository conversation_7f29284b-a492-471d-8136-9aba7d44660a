#!/usr/bin/env python3
"""
Advanced Usage Examples for CodeLlama Setup

This script demonstrates advanced usage patterns including custom prompts,
performance optimization, and integration with external tools.
"""

import sys
import time
import json
from pathlib import Path
from typing import List, Dict, Any

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from codellama_wrapper import CodeLlamaWrapper, GenerationResult
from config_manager import ConfigManager
from utils import (
    extract_code_from_response, validate_code_syntax, 
    save_generation_result, get_system_info
)


def example_custom_prompt_templates():
    """Example: Creating and using custom prompt templates"""
    print("=== Custom Prompt Templates ===")
    
    config_manager = ConfigManager()
    config = config_manager.load_default_config()
    
    # Add custom prompt templates
    config["prompts"]["code_review_template"] = """
{system_prompt}

Please review the following code and provide feedback:

```{language}
{code}
```

Focus on:
- Code quality and best practices
- Performance considerations
- Security issues
- Maintainability
- Potential bugs

Code review:
"""
    
    config["prompts"]["refactor_template"] = """
{system_prompt}

Refactor the following code to improve its quality:

```{language}
{code}
```

Refactoring goals:
{goals}

Provide the refactored code with explanations of changes made.

Refactored code:
"""
    
    # Save custom config
    custom_config_path = Path("custom_prompts_config.yaml")
    config_manager.save_config(config, custom_config_path)
    
    # Use custom templates
    wrapper = CodeLlamaWrapper(custom_config_path)
    
    # Example code to review
    code_to_review = """
def calculate_total(items):
    total = 0
    for i in range(len(items)):
        total = total + items[i]['price'] * items[i]['quantity']
    return total
"""
    
    # Use custom template
    review_prompt = config["prompts"]["code_review_template"].format(
        system_prompt=config["prompts"]["system_prompt"],
        code=code_to_review,
        language="python"
    )
    
    result = wrapper.generate(review_prompt)
    print("Code review result:")
    print(result.text[:300] + "...")
    
    # Clean up
    custom_config_path.unlink()


def example_performance_optimization():
    """Example: Performance optimization techniques"""
    print("\n=== Performance Optimization ===")
    
    # Test different configurations for performance
    configs = [
        {"name": "Standard", "temperature": 0.1, "max_tokens": 512},
        {"name": "Fast", "temperature": 0.05, "max_tokens": 256},
        {"name": "Quality", "temperature": 0.15, "max_tokens": 1024}
    ]
    
    prompt = "Create a function to sort a list of dictionaries by a key"
    
    for config in configs:
        print(f"\nTesting {config['name']} configuration:")
        
        wrapper = CodeLlamaWrapper()
        
        start_time = time.time()
        result = wrapper.generate_code(
            prompt,
            temperature=config["temperature"],
            max_new_tokens=config["max_tokens"]
        )
        end_time = time.time()
        
        print(f"  Time: {end_time - start_time:.2f}s")
        print(f"  Tokens: {result.token_count}")
        print(f"  Tokens/sec: {result.token_count / result.generation_time:.1f}")
        print(f"  Code length: {len(result.text)} chars")


def example_batch_processing_with_validation():
    """Example: Batch processing with code validation"""
    print("\n=== Batch Processing with Validation ===")
    
    wrapper = CodeLlamaWrapper()
    
    # Define batch tasks
    tasks = [
        {
            "id": "task1",
            "description": "Create a function to calculate fibonacci numbers",
            "language": "python",
            "validate": True
        },
        {
            "id": "task2", 
            "description": "Create a binary search function",
            "language": "python",
            "validate": True
        },
        {
            "id": "task3",
            "description": "Create a function to reverse a linked list",
            "language": "python",
            "validate": True
        }
    ]
    
    results = []
    
    for task in tasks:
        print(f"Processing {task['id']}: {task['description'][:40]}...")
        
        # Generate code
        result = wrapper.generate_code(
            task["description"],
            language=task["language"]
        )
        
        # Extract and validate code if requested
        if task.get("validate"):
            code_blocks = extract_code_from_response(result.text)
            
            validation_results = []
            for lang, code in code_blocks:
                is_valid, error = validate_code_syntax(code, task["language"])
                validation_results.append({
                    "language": lang or task["language"],
                    "valid": is_valid,
                    "error": error,
                    "code": code
                })
            
            task_result = {
                "task": task,
                "result": result,
                "validation": validation_results,
                "valid_code_found": any(v["valid"] for v in validation_results)
            }
        else:
            task_result = {
                "task": task,
                "result": result,
                "validation": None,
                "valid_code_found": None
            }
        
        results.append(task_result)
        
        # Print validation summary
        if task.get("validate"):
            valid_count = sum(1 for v in validation_results if v["valid"])
            print(f"  Validation: {valid_count}/{len(validation_results)} code blocks valid")
    
    # Summary
    print(f"\nBatch processing complete:")
    print(f"  Total tasks: {len(results)}")
    valid_tasks = sum(1 for r in results if r.get("valid_code_found"))
    print(f"  Tasks with valid code: {valid_tasks}")
    
    return results


def example_caching_and_memory_management():
    """Example: Caching and memory management"""
    print("\n=== Caching and Memory Management ===")
    
    # Configure with caching enabled
    config_manager = ConfigManager()
    config = config_manager.load_default_config()
    config["performance"]["enable_cache"] = True
    config["performance"]["cache_size"] = 5
    config["performance"]["cleanup_after_generation"] = True
    
    # Save config
    cache_config_path = Path("cache_config.yaml")
    config_manager.save_config(config, cache_config_path)
    
    wrapper = CodeLlamaWrapper(cache_config_path)
    
    # Test caching
    prompt = "Create a simple calculator function"
    
    print("First generation (no cache):")
    start_time = time.time()
    result1 = wrapper.generate_code(prompt)
    time1 = time.time() - start_time
    print(f"  Time: {time1:.2f}s")
    
    print("Second generation (should use cache):")
    start_time = time.time()
    result2 = wrapper.generate_code(prompt)
    time2 = time.time() - start_time
    print(f"  Time: {time2:.2f}s")
    
    print(f"Cache speedup: {time1/time2:.1f}x faster")
    print(f"Results identical: {result1.text == result2.text}")
    
    # Show cache stats
    print(f"Cache size: {len(wrapper.generation_cache)}")
    
    # Clear cache
    wrapper.clear_cache()
    print("Cache cleared")
    
    # Clean up
    cache_config_path.unlink()


def example_error_handling_and_recovery():
    """Example: Error handling and recovery strategies"""
    print("\n=== Error Handling and Recovery ===")
    
    wrapper = CodeLlamaWrapper()
    
    # Test with various problematic inputs
    test_cases = [
        {
            "name": "Very long prompt",
            "prompt": "Create a function " + "that does something " * 100,
            "max_tokens": 50
        },
        {
            "name": "Empty prompt",
            "prompt": "",
            "max_tokens": 100
        },
        {
            "name": "Special characters",
            "prompt": "Create a function with émojis 🚀 and spëcial chars",
            "max_tokens": 200
        }
    ]
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['name']}")
        
        try:
            result = wrapper.generate_code(
                test_case["prompt"],
                max_new_tokens=test_case["max_tokens"]
            )
            print(f"  Success: Generated {result.token_count} tokens")
            
        except Exception as e:
            print(f"  Error: {type(e).__name__}: {e}")
            
            # Recovery strategy: try with simpler parameters
            try:
                print("  Attempting recovery with simpler parameters...")
                result = wrapper.generate_code(
                    "Create a simple function",
                    max_new_tokens=100,
                    temperature=0.1
                )
                print(f"  Recovery successful: {result.token_count} tokens")
                
            except Exception as recovery_error:
                print(f"  Recovery failed: {recovery_error}")


def example_integration_with_external_tools():
    """Example: Integration with external tools and workflows"""
    print("\n=== Integration with External Tools ===")
    
    wrapper = CodeLlamaWrapper()
    
    # Simulate a development workflow
    workflow_steps = [
        {
            "step": "Generate initial code",
            "action": "generate",
            "prompt": "Create a class for managing a todo list"
        },
        {
            "step": "Add error handling",
            "action": "enhance",
            "prompt": "Add comprehensive error handling to the todo list class"
        },
        {
            "step": "Add documentation",
            "action": "document",
            "prompt": "Add detailed docstrings and type hints"
        }
    ]
    
    workflow_results = []
    current_code = ""
    
    for step in workflow_steps:
        print(f"\n{step['step']}:")
        
        if step["action"] == "generate":
            result = wrapper.generate_code(step["prompt"], language="python")
            current_code = result.text
            
        elif step["action"] == "enhance":
            # Use the current code as context
            enhance_prompt = f"""
Enhance the following code by {step['prompt']}:

```python
{current_code}
```

Enhanced code:
"""
            result = wrapper.generate(enhance_prompt)
            # Extract new code
            code_blocks = extract_code_from_response(result.text)
            if code_blocks:
                current_code = code_blocks[0][1]
            
        elif step["action"] == "document":
            # Add documentation to existing code
            doc_prompt = f"""
Add comprehensive documentation to the following code:

```python
{current_code}
```

Documented code:
"""
            result = wrapper.generate(doc_prompt)
            code_blocks = extract_code_from_response(result.text)
            if code_blocks:
                current_code = code_blocks[0][1]
        
        workflow_results.append({
            "step": step["step"],
            "result": result,
            "code": current_code[:200] + "..." if len(current_code) > 200 else current_code
        })
        
        print(f"  Generated {result.token_count} tokens")
    
    print(f"\nWorkflow completed with {len(workflow_steps)} steps")
    
    # Save final result
    output_dir = Path("workflow_output")
    output_dir.mkdir(exist_ok=True)
    
    final_file = output_dir / "final_todo_class.py"
    with open(final_file, 'w') as f:
        f.write(current_code)
    
    print(f"Final code saved to: {final_file}")
    
    return workflow_results


def main():
    """Run all advanced usage examples"""
    print("CodeLlama Setup - Advanced Usage Examples")
    print("=" * 60)
    
    try:
        example_custom_prompt_templates()
        example_performance_optimization()
        batch_results = example_batch_processing_with_validation()
        example_caching_and_memory_management()
        example_error_handling_and_recovery()
        workflow_results = example_integration_with_external_tools()
        
        print("\n" + "=" * 60)
        print("Advanced usage examples completed!")
        print(f"Batch processing: {len(batch_results)} tasks completed")
        print(f"Workflow: {len(workflow_results)} steps completed")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
