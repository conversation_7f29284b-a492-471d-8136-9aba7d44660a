"""
Configuration Manager for CodeLlama Setup

This module handles loading, validating, and managing configuration files
for the CodeLlama setup. It supports YAML configuration files with JSON
schema validation and provides utilities for merging configurations.
"""

import os
import json
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from copy import deepcopy

try:
    import jsonschema
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False
    logging.warning("jsonschema not available. Configuration validation will be skipped.")


class ConfigurationError(Exception):
    """Custom exception for configuration-related errors."""
    pass


class ConfigManager:
    """
    Manages configuration loading, validation, and merging for CodeLlama setup.
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_dir: Directory containing configuration files. 
                       Defaults to 'config' in the project root.
        """
        if config_dir is None:
            # Get the project root directory (parent of src)
            project_root = Path(__file__).parent.parent
            config_dir = project_root / "config"
        
        self.config_dir = Path(config_dir)
        self.schema_path = self.config_dir / "schemas" / "config_schema.json"
        self.default_config_path = self.config_dir / "default_config.yaml"
        
        self._schema = None
        self._default_config = None
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
    
    def load_schema(self) -> Dict[str, Any]:
        """
        Load the JSON schema for configuration validation.
        
        Returns:
            The loaded schema as a dictionary.
            
        Raises:
            ConfigurationError: If schema file cannot be loaded.
        """
        if self._schema is not None:
            return self._schema
        
        try:
            with open(self.schema_path, 'r') as f:
                self._schema = json.load(f)
            return self._schema
        except FileNotFoundError:
            raise ConfigurationError(f"Schema file not found: {self.schema_path}")
        except json.JSONDecodeError as e:
            raise ConfigurationError(f"Invalid JSON in schema file: {e}")
    
    def load_default_config(self) -> Dict[str, Any]:
        """
        Load the default configuration.
        
        Returns:
            The default configuration as a dictionary.
            
        Raises:
            ConfigurationError: If default config file cannot be loaded.
        """
        if self._default_config is not None:
            return self._default_config
        
        try:
            with open(self.default_config_path, 'r') as f:
                self._default_config = yaml.safe_load(f)
            return self._default_config
        except FileNotFoundError:
            raise ConfigurationError(f"Default config file not found: {self.default_config_path}")
        except yaml.YAMLError as e:
            raise ConfigurationError(f"Invalid YAML in default config: {e}")
    
    def load_config(self, config_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """
        Load configuration from a file, merging with defaults.
        
        Args:
            config_path: Path to the configuration file. If None, uses default config.
            
        Returns:
            The loaded and merged configuration.
            
        Raises:
            ConfigurationError: If configuration cannot be loaded or is invalid.
        """
        # Start with default configuration
        config = deepcopy(self.load_default_config())
        
        # If a custom config path is provided, merge it with defaults
        if config_path is not None:
            config_path = Path(config_path)
            
            if not config_path.exists():
                raise ConfigurationError(f"Configuration file not found: {config_path}")
            
            try:
                with open(config_path, 'r') as f:
                    if config_path.suffix.lower() in ['.yaml', '.yml']:
                        custom_config = yaml.safe_load(f)
                    elif config_path.suffix.lower() == '.json':
                        custom_config = json.load(f)
                    else:
                        raise ConfigurationError(f"Unsupported config file format: {config_path.suffix}")
                
                # Merge custom config with defaults
                config = self._deep_merge(config, custom_config)
                
            except (yaml.YAMLError, json.JSONDecodeError) as e:
                raise ConfigurationError(f"Invalid configuration file format: {e}")
        
        # Validate the final configuration
        self.validate_config(config)
        
        return config
    
    def validate_config(self, config: Dict[str, Any]) -> None:
        """
        Validate configuration against the schema.
        
        Args:
            config: Configuration dictionary to validate.
            
        Raises:
            ConfigurationError: If configuration is invalid.
        """
        if not JSONSCHEMA_AVAILABLE:
            self.logger.warning("Skipping configuration validation (jsonschema not available)")
            return
        
        try:
            schema = self.load_schema()
            jsonschema.validate(config, schema)
        except jsonschema.ValidationError as e:
            raise ConfigurationError(f"Configuration validation failed: {e.message}")
        except jsonschema.SchemaError as e:
            raise ConfigurationError(f"Invalid schema: {e.message}")
    
    def save_config(self, config: Dict[str, Any], output_path: Union[str, Path]) -> None:
        """
        Save configuration to a file.
        
        Args:
            config: Configuration dictionary to save.
            output_path: Path where to save the configuration.
            
        Raises:
            ConfigurationError: If configuration cannot be saved.
        """
        output_path = Path(output_path)
        
        # Validate before saving
        self.validate_config(config)
        
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w') as f:
                if output_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(config, f, default_flow_style=False, indent=2)
                elif output_path.suffix.lower() == '.json':
                    json.dump(config, f, indent=2)
                else:
                    raise ConfigurationError(f"Unsupported output format: {output_path.suffix}")
                    
        except Exception as e:
            raise ConfigurationError(f"Failed to save configuration: {e}")
    
    def get_mode_config(self, config: Dict[str, Any], mode: str = "default") -> Dict[str, Any]:
        """
        Get configuration for a specific mode, merging mode-specific settings.
        
        Args:
            config: Base configuration dictionary.
            mode: Mode name to apply.
            
        Returns:
            Configuration with mode-specific settings applied.
            
        Raises:
            ConfigurationError: If mode is not found.
        """
        if "modes" not in config or mode not in config["modes"]:
            if mode != "default":
                raise ConfigurationError(f"Mode '{mode}' not found in configuration")
            return config
        
        # Create a copy of the base config
        mode_config = deepcopy(config)
        
        # Apply mode-specific settings
        mode_settings = config["modes"][mode]
        
        # Merge mode settings into appropriate sections
        for key, value in mode_settings.items():
            if key in ["temperature", "top_p", "top_k", "max_new_tokens", "do_sample", 
                      "repetition_penalty", "num_beams", "early_stopping", "length_penalty"]:
                mode_config["generation"][key] = value
            elif key in ["include_explanations", "include_comments", "response_format"]:
                mode_config["formatting"][key] = value
            elif key in ["log_generation_details", "log_timing"]:
                mode_config["logging"][key] = value
            else:
                # For other settings, try to place them in the appropriate section
                # or create a new section if needed
                if "mode_overrides" not in mode_config:
                    mode_config["mode_overrides"] = {}
                mode_config["mode_overrides"][key] = value
        
        return mode_config
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deep merge two dictionaries, with override values taking precedence.
        
        Args:
            base: Base dictionary.
            override: Dictionary with override values.
            
        Returns:
            Merged dictionary.
        """
        result = deepcopy(base)
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = deepcopy(value)
        
        return result
    
    def list_available_modes(self, config: Dict[str, Any]) -> list:
        """
        Get list of available modes from configuration.
        
        Args:
            config: Configuration dictionary.
            
        Returns:
            List of available mode names.
        """
        if "modes" not in config:
            return ["default"]
        
        return list(config["modes"].keys())
    
    def create_example_config(self, output_path: Union[str, Path]) -> None:
        """
        Create an example configuration file with common customizations.
        
        Args:
            output_path: Path where to save the example configuration.
        """
        example_config = {
            "model": {
                "name": "codellama/CodeLlama-7b-Python-hf",
                "device": "auto",
                "torch_dtype": "float16"
            },
            "generation": {
                "max_new_tokens": 1024,
                "temperature": 0.2,
                "top_p": 0.9
            },
            "prompts": {
                "system_prompt": "You are an expert Python developer. Write clean, efficient, and well-documented code."
            },
            "formatting": {
                "include_explanations": True,
                "default_language": "python"
            },
            "modes": {
                "creative": {
                    "temperature": 0.8,
                    "max_new_tokens": 1024
                },
                "precise": {
                    "temperature": 0.0,
                    "do_sample": False
                }
            }
        }
        
        self.save_config(example_config, output_path)
