#!/usr/bin/env python3
"""
Ollama Integration for CodeLlama Setup
Provides a wrapper to control CodeLlama through Ollama API
"""

import json
import requests
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class OllamaResponse:
    """Response from Ollama API"""
    text: str
    model: str
    created_at: str
    done: bool
    total_duration: Optional[int] = None
    load_duration: Optional[int] = None
    prompt_eval_count: Optional[int] = None
    eval_count: Optional[int] = None
    eval_duration: Optional[int] = None

class OllamaWrapper:
    """
    Wrapper for controlling CodeLlama through Ollama API
    """
    
    def __init__(self, 
                 base_url: str = "http://localhost:11434",
                 model: str = "codellama:7b",
                 config_path: Optional[str] = None):
        """
        Initialize Ollama wrapper
        
        Args:
            base_url: Ollama server URL
            model: Model name to use
            config_path: Path to configuration file
        """
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.session = requests.Session()
        
        # Load configuration if provided
        if config_path:
            self.config = self._load_config(config_path)
        else:
            self.config = self._default_config()
            
        logger.info(f"Ollama wrapper initialized with model: {self.model}")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for Ollama"""
        return {
            "temperature": 0.1,
            "top_p": 0.9,
            "top_k": 40,
            "num_predict": 512,
            "repeat_penalty": 1.05,
            "stop": ["</code>", "```"],
        }
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file"""
        try:
            with open(config_path, 'r') as f:
                if config_path.endswith('.json'):
                    return json.load(f)
                elif config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    import yaml
                    return yaml.safe_load(f)
                else:
                    raise ValueError("Config file must be JSON or YAML")
        except Exception as e:
            logger.warning(f"Failed to load config: {e}. Using defaults.")
            return self._default_config()
    
    def is_available(self) -> bool:
        """Check if Ollama server is available"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama server not available: {e}")
            return False
    
    def list_models(self) -> List[str]:
        """List available models"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                return [model['name'] for model in data.get('models', [])]
            return []
        except Exception as e:
            logger.error(f"Failed to list models: {e}")
            return []
    
    def pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/pull",
                json={"name": model_name},
                stream=True
            )
            
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        data = json.loads(line)
                        if data.get('status'):
                            print(f"Pull status: {data['status']}")
                        if data.get('error'):
                            logger.error(f"Pull error: {data['error']}")
                            return False
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to pull model: {e}")
            return False
    
    def generate(self, 
                 prompt: str, 
                 system_prompt: Optional[str] = None,
                 **kwargs) -> OllamaResponse:
        """
        Generate text using Ollama API
        
        Args:
            prompt: Input prompt
            system_prompt: System prompt (optional)
            **kwargs: Additional parameters
        
        Returns:
            OllamaResponse object
        """
        # Merge config with kwargs
        options = {**self.config, **kwargs}
        
        # Prepare request data
        data = {
            "model": self.model,
            "prompt": prompt,
            "options": options,
            "stream": False
        }
        
        if system_prompt:
            data["system"] = system_prompt
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                return OllamaResponse(
                    text=result.get('response', ''),
                    model=result.get('model', self.model),
                    created_at=result.get('created_at', ''),
                    done=result.get('done', True),
                    total_duration=result.get('total_duration'),
                    load_duration=result.get('load_duration'),
                    prompt_eval_count=result.get('prompt_eval_count'),
                    eval_count=result.get('eval_count'),
                    eval_duration=result.get('eval_duration')
                )
            else:
                raise Exception(f"API request failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            raise
    
    def generate_code(self, 
                      description: str, 
                      language: str = "python",
                      **kwargs) -> OllamaResponse:
        """Generate code from description"""
        system_prompt = f"""You are an expert {language} programmer. 
Generate clean, efficient, and well-documented {language} code based on the given description.
Only return the code without explanations unless specifically asked."""
        
        prompt = f"Create {language} code for: {description}"
        
        return self.generate(prompt, system_prompt=system_prompt, **kwargs)
    
    def complete_code(self, 
                      partial_code: str, 
                      language: str = "python",
                      **kwargs) -> OllamaResponse:
        """Complete partial code"""
        system_prompt = f"""You are an expert {language} programmer.
Complete the following {language} code. Only return the completion, not the original code."""
        
        prompt = f"Complete this {language} code:\n\n{partial_code}"
        
        return self.generate(prompt, system_prompt=system_prompt, **kwargs)
    
    def explain_code(self, 
                     code: str, 
                     language: str = "python",
                     **kwargs) -> OllamaResponse:
        """Explain code functionality"""
        system_prompt = f"""You are an expert {language} programmer.
Explain what the following {language} code does in clear, concise terms."""
        
        prompt = f"Explain this {language} code:\n\n{code}"
        
        return self.generate(prompt, system_prompt=system_prompt, **kwargs)
    
    def debug_code(self, 
                   code: str, 
                   error: str, 
                   language: str = "python",
                   **kwargs) -> OllamaResponse:
        """Debug code with error"""
        system_prompt = f"""You are an expert {language} programmer and debugger.
Analyze the code and error, then provide a fixed version of the code."""
        
        prompt = f"""Debug this {language} code:

Code:
{code}

Error:
{error}

Provide the corrected code:"""
        
        return self.generate(prompt, system_prompt=system_prompt, **kwargs)
    
    def chat(self, 
             messages: List[Dict[str, str]], 
             **kwargs) -> OllamaResponse:
        """
        Chat with the model using conversation history
        
        Args:
            messages: List of message dicts with 'role' and 'content'
            **kwargs: Additional parameters
        """
        options = {**self.config, **kwargs}
        
        data = {
            "model": self.model,
            "messages": messages,
            "options": options,
            "stream": False
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/chat",
                json=data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                message = result.get('message', {})
                return OllamaResponse(
                    text=message.get('content', ''),
                    model=result.get('model', self.model),
                    created_at=result.get('created_at', ''),
                    done=result.get('done', True),
                    total_duration=result.get('total_duration'),
                    load_duration=result.get('load_duration'),
                    prompt_eval_count=result.get('prompt_eval_count'),
                    eval_count=result.get('eval_count'),
                    eval_duration=result.get('eval_duration')
                )
            else:
                raise Exception(f"Chat request failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Chat failed: {e}")
            raise

def main():
    """Example usage"""
    # Initialize wrapper
    ollama = OllamaWrapper()
    
    # Check if server is available
    if not ollama.is_available():
        print("❌ Ollama server is not available")
        print("Please start Ollama with: ollama serve")
        return
    
    print("✅ Ollama server is available")
    
    # List models
    models = ollama.list_models()
    print(f"📋 Available models: {models}")
    
    # Generate code
    try:
        print("\n🔧 Generating code...")
        response = ollama.generate_code("Create a function to calculate fibonacci numbers")
        print(f"Generated code:\n{response.text}")
        print(f"Generation time: {response.eval_duration/1000000:.2f}ms")
    except Exception as e:
        print(f"❌ Generation failed: {e}")

if __name__ == "__main__":
    main()
