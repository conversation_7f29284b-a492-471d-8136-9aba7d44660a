"""
CodeLlama Wrapper

This module provides a high-level interface for interacting with CodeLlama models
with customizable configuration and easy-to-use methods for various code generation tasks.
"""

import os
import re
import time
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass

import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    GenerationConfig,
    StoppingCriteria,
    StoppingCriteriaList
)

try:
    from .config_manager import ConfigManager, ConfigurationError
except ImportError:
    from config_manager import ConfigManager, ConfigurationError


@dataclass
class GenerationResult:
    """Container for generation results with metadata."""
    text: str
    prompt: str
    generation_time: float
    token_count: int
    model_name: str
    config_used: Dict[str, Any]


class StopSequenceCriteria(StoppingCriteria):
    """Custom stopping criteria for specific sequences."""
    
    def __init__(self, stop_sequences: List[str], tokenizer):
        self.stop_sequences = stop_sequences
        self.tokenizer = tokenizer
    
    def __call__(self, input_ids: torch.LongTensor, scores: torch.FloatTensor, **kwargs) -> bool:
        # Decode the last few tokens to check for stop sequences
        last_tokens = input_ids[0][-50:]  # Check last 50 tokens
        decoded = self.tokenizer.decode(last_tokens, skip_special_tokens=True)
        
        for stop_seq in self.stop_sequences:
            if stop_seq in decoded:
                return True
        return False


class CodeLlamaWrapper:
    """
    High-level wrapper for CodeLlama models with configuration management.
    """
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None, mode: str = "default"):
        """
        Initialize the CodeLlama wrapper.
        
        Args:
            config_path: Path to configuration file. Uses default if None.
            mode: Configuration mode to use.
        """
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self.config_manager = ConfigManager()
        self.config = self.config_manager.load_config(config_path)
        self.config = self.config_manager.get_mode_config(self.config, mode)
        
        # Initialize model and tokenizer
        self.model = None
        self.tokenizer = None
        self.generation_config = None
        
        # Performance tracking
        self.generation_cache = {}
        self.stats = {
            "total_generations": 0,
            "total_tokens": 0,
            "total_time": 0.0
        }
        
        # Setup logging
        self._setup_logging()
        
        self.logger.info(f"CodeLlama wrapper initialized with mode: {mode}")
    
    def _setup_logging(self):
        """Setup logging based on configuration."""
        log_level = getattr(logging, self.config["logging"]["level"])
        logging.basicConfig(level=log_level)

        log_file = self.config["logging"].get("log_file")
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(log_level)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def load_model(self) -> None:
        """
        Load the model and tokenizer based on configuration.
        
        Raises:
            ConfigurationError: If model loading fails.
        """
        if self.model is not None:
            self.logger.info("Model already loaded")
            return
        
        model_config = self.config["model"]
        
        try:
            self.logger.info(f"Loading model: {model_config['name']}")
            start_time = time.time()
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_config["name"],
                cache_dir=model_config.get("cache_dir"),
                trust_remote_code=model_config.get("trust_remote_code", False)
            )
            
            # Set pad token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Prepare model loading arguments
            model_kwargs = {
                "cache_dir": model_config.get("cache_dir"),
                "trust_remote_code": model_config.get("trust_remote_code", False),
                "device_map": model_config.get("device_map", "auto"),
            }
            
            # Handle torch dtype
            if model_config.get("torch_dtype") == "auto":
                model_kwargs["torch_dtype"] = torch.float16 if torch.cuda.is_available() else torch.float32
            elif model_config.get("torch_dtype"):
                model_kwargs["torch_dtype"] = getattr(torch, model_config["torch_dtype"])
            
            # Handle quantization
            if model_config.get("load_in_8bit"):
                model_kwargs["load_in_8bit"] = True
            elif model_config.get("load_in_4bit"):
                model_kwargs["load_in_4bit"] = True
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                model_config["name"],
                **model_kwargs
            )
            
            # Setup generation config
            self._setup_generation_config()
            
            load_time = time.time() - start_time
            self.logger.info(f"Model loaded successfully in {load_time:.2f} seconds")
            
        except Exception as e:
            raise ConfigurationError(f"Failed to load model: {e}")
    
    def _setup_generation_config(self):
        """Setup generation configuration from config."""
        gen_config = self.config["generation"]
        
        self.generation_config = GenerationConfig(
            max_new_tokens=gen_config.get("max_new_tokens", 512),
            max_length=gen_config.get("max_length"),
            temperature=gen_config.get("temperature", 0.1),
            top_p=gen_config.get("top_p", 0.95),
            top_k=gen_config.get("top_k", 50),
            repetition_penalty=gen_config.get("repetition_penalty", 1.1),
            do_sample=gen_config.get("do_sample", True),
            num_beams=gen_config.get("num_beams", 1),
            early_stopping=gen_config.get("early_stopping", False),
            length_penalty=gen_config.get("length_penalty", 1.0),
            pad_token_id=gen_config.get("pad_token_id") or self.tokenizer.pad_token_id,
            eos_token_id=gen_config.get("eos_token_id") or self.tokenizer.eos_token_id,
        )
    
    def generate(self, prompt: str, **kwargs) -> GenerationResult:
        """
        Generate text from a prompt.
        
        Args:
            prompt: Input prompt for generation.
            **kwargs: Override generation parameters.
            
        Returns:
            GenerationResult with the generated text and metadata.
        """
        if self.model is None:
            self.load_model()
        
        start_time = time.time()
        
        # Check cache if enabled
        cache_key = hash(prompt + str(sorted(kwargs.items())))
        if (self.config["performance"].get("enable_cache", True) and
            cache_key in self.generation_cache):
            self.logger.debug("Using cached result")
            return self.generation_cache[cache_key]
        
        # Prepare generation config with overrides
        gen_config = GenerationConfig(**self.generation_config.to_dict())
        for key, value in kwargs.items():
            if hasattr(gen_config, key):
                setattr(gen_config, key, value)
        
        # Tokenize input
        inputs = self.tokenizer(prompt, return_tensors="pt")
        if torch.cuda.is_available() and self.model.device.type == "cuda":
            inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
        
        # Setup stopping criteria
        stopping_criteria = StoppingCriteriaList()
        stop_sequences = self.config["generation"].get("stop_sequences", [])
        if stop_sequences:
            stopping_criteria.append(StopSequenceCriteria(stop_sequences, self.tokenizer))
        
        # Generate
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                generation_config=gen_config,
                stopping_criteria=stopping_criteria if stop_sequences else None,
                return_dict_in_generate=True,
                output_scores=True
            )
        
        # Decode output
        generated_tokens = outputs.sequences[0][len(inputs["input_ids"][0]):]
        generated_text = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)
        
        # Clean up output
        generated_text = self._post_process_output(generated_text)
        
        generation_time = time.time() - start_time
        token_count = len(generated_tokens)
        
        # Create result
        result = GenerationResult(
            text=generated_text,
            prompt=prompt,
            generation_time=generation_time,
            token_count=token_count,
            model_name=self.config["model"]["name"],
            config_used=gen_config.to_dict()
        )
        
        # Update stats
        self.stats["total_generations"] += 1
        self.stats["total_tokens"] += token_count
        self.stats["total_time"] += generation_time
        
        # Cache result if enabled
        if self.config["performance"].get("enable_cache", True):
            cache_size = self.config["performance"].get("cache_size", 100)
            if len(self.generation_cache) >= cache_size:
                # Remove oldest entry
                oldest_key = next(iter(self.generation_cache))
                del self.generation_cache[oldest_key]
            self.generation_cache[cache_key] = result
        
        # Log generation details if enabled
        if self.config["logging"].get("log_generation_details", False):
            self.logger.info(f"Generated {token_count} tokens in {generation_time:.2f}s")

        # Cleanup if configured
        if self.config["performance"].get("cleanup_after_generation", True):
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        return result
    
    def _post_process_output(self, text: str) -> str:
        """Post-process generated text based on formatting configuration."""
        formatting = self.config["formatting"]
        
        if formatting.get("strip_extra_whitespace", True):
            # Remove excessive whitespace
            text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
            text = text.strip()
        
        return text
    
    def complete_code(self, code: str, language: str = None, **kwargs) -> GenerationResult:
        """
        Complete partial code.
        
        Args:
            code: Partial code to complete.
            language: Programming language (auto-detected if None).
            **kwargs: Generation parameters.
            
        Returns:
            GenerationResult with completed code.
        """
        if language is None:
            language = self._detect_language(code)
        
        template = self.config["prompts"]["code_completion_template"]
        prompt = template.format(
            system_prompt=self.config["prompts"]["system_prompt"],
            code=code,
            language=language
        )
        
        return self.generate(prompt, **kwargs)
    
    def explain_code(self, code: str, language: str = None, **kwargs) -> GenerationResult:
        """
        Generate explanation for code.
        
        Args:
            code: Code to explain.
            language: Programming language (auto-detected if None).
            **kwargs: Generation parameters.
            
        Returns:
            GenerationResult with code explanation.
        """
        if language is None:
            language = self._detect_language(code)
        
        template = self.config["prompts"]["code_explanation_template"]
        prompt = template.format(
            system_prompt=self.config["prompts"]["system_prompt"],
            code=code,
            language=language
        )
        
        return self.generate(prompt, **kwargs)
    
    def generate_code(self, description: str, language: str = None, 
                     requirements: str = "", **kwargs) -> GenerationResult:
        """
        Generate code from description.
        
        Args:
            description: Description of what the code should do.
            language: Target programming language.
            requirements: Additional requirements or constraints.
            **kwargs: Generation parameters.
            
        Returns:
            GenerationResult with generated code.
        """
        if language is None:
            language = self.config["formatting"]["default_language"]
        
        template = self.config["prompts"]["code_generation_template"]
        prompt = template.format(
            system_prompt=self.config["prompts"]["system_prompt"],
            description=description,
            language=language,
            requirements=requirements
        )
        
        return self.generate(prompt, **kwargs)
    
    def debug_code(self, code: str, error: str, language: str = None, **kwargs) -> GenerationResult:
        """
        Help debug code with error information.
        
        Args:
            code: Code with issues.
            error: Error message or description of the problem.
            language: Programming language (auto-detected if None).
            **kwargs: Generation parameters.
            
        Returns:
            GenerationResult with debugging assistance.
        """
        if language is None:
            language = self._detect_language(code)
        
        template = self.config["prompts"]["debug_template"]
        prompt = template.format(
            system_prompt=self.config["prompts"]["system_prompt"],
            code=code,
            error=error,
            language=language
        )
        
        return self.generate(prompt, **kwargs)
    
    def _detect_language(self, code: str) -> str:
        """
        Auto-detect programming language from code.
        
        Args:
            code: Code snippet to analyze.
            
        Returns:
            Detected language name.
        """
        if not self.config["formatting"]["auto_detect_language"]:
            return self.config["formatting"]["default_language"]
        
        # Simple heuristics for language detection
        code_lower = code.lower()
        
        if any(keyword in code_lower for keyword in ["def ", "import ", "from ", "class "]):
            return "python"
        elif any(keyword in code_lower for keyword in ["function", "var ", "let ", "const "]):
            return "javascript"
        elif any(keyword in code_lower for keyword in ["public class", "private ", "public static"]):
            return "java"
        elif any(keyword in code_lower for keyword in ["#include", "int main", "std::"]):
            return "cpp"
        elif any(keyword in code_lower for keyword in ["package ", "func ", "import "]):
            return "go"
        
        return self.config["formatting"]["default_language"]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get generation statistics."""
        return self.stats.copy()
    
    def clear_cache(self) -> None:
        """Clear the generation cache."""
        self.generation_cache.clear()
        self.logger.info("Generation cache cleared")
    
    def unload_model(self) -> None:
        """Unload the model to free memory."""
        if self.model is not None:
            del self.model
            del self.tokenizer
            self.model = None
            self.tokenizer = None
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            self.logger.info("Model unloaded")
