"""
Utility functions for the CodeLlama setup.

This module provides various utility functions for text processing,
file handling, and other common operations.
"""

import os
import re
import time
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime

import torch


def format_code_block(code: str, language: str = "", style: str = "markdown") -> str:
    """
    Format code in a code block with syntax highlighting markers.
    
    Args:
        code: Code to format.
        language: Programming language for syntax highlighting.
        style: Format style ("markdown", "plain", "highlighted").
        
    Returns:
        Formatted code block.
    """
    if style == "markdown":
        return f"```{language}\n{code}\n```"
    elif style == "highlighted":
        # For terminal output with basic highlighting
        return f"[{language.upper()}]\n{code}\n[/{language.upper()}]"
    else:  # plain
        return code


def extract_code_from_response(text: str) -> List[Tuple[str, str]]:
    """
    Extract code blocks from generated text.
    
    Args:
        text: Text containing code blocks.
        
    Returns:
        List of tuples (language, code) for each code block found.
    """
    # Pattern to match markdown code blocks
    pattern = r'```(\w*)\n(.*?)\n```'
    matches = re.findall(pattern, text, re.DOTALL)
    
    # Also look for code without explicit blocks (indented code)
    if not matches:
        # Look for indented code blocks
        lines = text.split('\n')
        code_lines = []
        in_code_block = False
        
        for line in lines:
            if line.startswith('    ') or line.startswith('\t'):
                code_lines.append(line.lstrip())
                in_code_block = True
            elif in_code_block and line.strip() == '':
                code_lines.append('')
            elif in_code_block:
                break
        
        if code_lines:
            code = '\n'.join(code_lines).strip()
            if code:
                matches = [('', code)]
    
    return matches


def detect_file_language(file_path: Union[str, Path]) -> str:
    """
    Detect programming language from file extension.
    
    Args:
        file_path: Path to the file.
        
    Returns:
        Detected language name.
    """
    file_path = Path(file_path)
    extension = file_path.suffix.lower()
    
    language_map = {
        '.py': 'python',
        '.js': 'javascript',
        '.jsx': 'javascript',
        '.ts': 'typescript',
        '.tsx': 'typescript',
        '.java': 'java',
        '.cpp': 'cpp',
        '.cc': 'cpp',
        '.cxx': 'cpp',
        '.c++': 'cpp',
        '.c': 'c',
        '.h': 'c',
        '.hpp': 'cpp',
        '.cs': 'csharp',
        '.go': 'go',
        '.rs': 'rust',
        '.php': 'php',
        '.rb': 'ruby',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.scala': 'scala',
        '.r': 'r',
        '.R': 'r',
        '.m': 'matlab',
        '.sql': 'sql',
        '.html': 'html',
        '.htm': 'html',
        '.css': 'css',
        '.scss': 'css',
        '.sass': 'css',
        '.sh': 'shell',
        '.bash': 'shell',
        '.zsh': 'shell',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.json': 'json',
        '.xml': 'xml',
        '.md': 'markdown',
        '.markdown': 'markdown'
    }
    
    return language_map.get(extension, 'text')


def clean_generated_code(code: str, language: str = "") -> str:
    """
    Clean and format generated code.
    
    Args:
        code: Raw generated code.
        language: Programming language for language-specific cleaning.
        
    Returns:
        Cleaned code.
    """
    # Remove common artifacts from generation
    code = re.sub(r'^```\w*\n?', '', code)  # Remove opening code block
    code = re.sub(r'\n?```$', '', code)     # Remove closing code block
    
    # Remove excessive blank lines
    code = re.sub(r'\n\s*\n\s*\n', '\n\n', code)
    
    # Language-specific cleaning
    if language.lower() == 'python':
        # Remove common Python generation artifacts
        code = re.sub(r'^# .*?generated.*?\n', '', code, flags=re.IGNORECASE)
        code = re.sub(r'^# .*?example.*?\n', '', code, flags=re.IGNORECASE)
    
    return code.strip()


def validate_code_syntax(code: str, language: str) -> Tuple[bool, Optional[str]]:
    """
    Basic syntax validation for generated code.
    
    Args:
        code: Code to validate.
        language: Programming language.
        
    Returns:
        Tuple of (is_valid, error_message).
    """
    if language.lower() == 'python':
        try:
            compile(code, '<string>', 'exec')
            return True, None
        except SyntaxError as e:
            return False, f"Python syntax error: {e}"
    
    # For other languages, do basic checks
    if language.lower() in ['javascript', 'typescript']:
        # Check for basic JS/TS syntax issues
        if code.count('{') != code.count('}'):
            return False, "Mismatched braces"
        if code.count('(') != code.count(')'):
            return False, "Mismatched parentheses"
    
    elif language.lower() in ['java', 'cpp', 'c', 'csharp']:
        # Check for basic C-style syntax issues
        if code.count('{') != code.count('}'):
            return False, "Mismatched braces"
        if code.count('(') != code.count(')'):
            return False, "Mismatched parentheses"
        if not code.rstrip().endswith((';', '}', ')')):
            return False, "Missing semicolon or closing brace"
    
    return True, None


def estimate_tokens(text: str) -> int:
    """
    Rough estimation of token count for text.
    
    Args:
        text: Text to estimate tokens for.
        
    Returns:
        Estimated token count.
    """
    # Rough approximation: ~4 characters per token for English text
    # Code might have different ratios, but this gives a ballpark
    return len(text) // 4


def format_generation_time(seconds: float) -> str:
    """
    Format generation time in a human-readable way.
    
    Args:
        seconds: Time in seconds.
        
    Returns:
        Formatted time string.
    """
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    else:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds:.1f}s"


def get_system_info() -> Dict[str, Any]:
    """
    Get system information relevant for model performance.
    
    Returns:
        Dictionary with system information.
    """
    info = {
        "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
        "torch_version": torch.__version__,
        "cuda_available": torch.cuda.is_available(),
        "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
    }
    
    if torch.cuda.is_available():
        info["cuda_version"] = torch.version.cuda
        info["gpu_names"] = [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())]
        info["gpu_memory"] = [
            f"{torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f}GB"
            for i in range(torch.cuda.device_count())
        ]
    
    return info


def create_cache_key(prompt: str, config: Dict[str, Any]) -> str:
    """
    Create a cache key for a prompt and configuration.
    
    Args:
        prompt: Input prompt.
        config: Generation configuration.
        
    Returns:
        Cache key string.
    """
    # Create a deterministic hash from prompt and relevant config
    relevant_config = {
        k: v for k, v in config.items() 
        if k in ['temperature', 'top_p', 'top_k', 'max_new_tokens', 'repetition_penalty']
    }
    
    cache_string = f"{prompt}|{sorted(relevant_config.items())}"
    return hashlib.md5(cache_string.encode()).hexdigest()


def safe_filename(text: str, max_length: int = 50) -> str:
    """
    Create a safe filename from text.
    
    Args:
        text: Text to convert to filename.
        max_length: Maximum filename length.
        
    Returns:
        Safe filename string.
    """
    # Remove or replace unsafe characters
    safe_text = re.sub(r'[^\w\s-]', '', text)
    safe_text = re.sub(r'[-\s]+', '-', safe_text)
    safe_text = safe_text.strip('-')
    
    # Truncate if too long
    if len(safe_text) > max_length:
        safe_text = safe_text[:max_length].rstrip('-')
    
    return safe_text or "untitled"


def load_file_content(file_path: Union[str, Path]) -> Tuple[str, str]:
    """
    Load file content and detect language.
    
    Args:
        file_path: Path to the file.
        
    Returns:
        Tuple of (content, detected_language).
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # Try with different encoding
        with open(file_path, 'r', encoding='latin-1') as f:
            content = f.read()
    
    language = detect_file_language(file_path)
    return content, language


def save_generation_result(result, output_dir: Union[str, Path], 
                          include_metadata: bool = True) -> Path:
    """
    Save generation result to file.
    
    Args:
        result: GenerationResult object.
        output_dir: Directory to save the result.
        include_metadata: Whether to include metadata in the output.
        
    Returns:
        Path to the saved file.
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create filename from prompt
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    prompt_snippet = safe_filename(result.prompt[:30])
    filename = f"{timestamp}_{prompt_snippet}.txt"
    
    output_path = output_dir / filename
    
    with open(output_path, 'w', encoding='utf-8') as f:
        if include_metadata:
            f.write(f"# Generation Result\n")
            f.write(f"# Model: {result.model_name}\n")
            f.write(f"# Time: {format_generation_time(result.generation_time)}\n")
            f.write(f"# Tokens: {result.token_count}\n")
            f.write(f"# Generated: {datetime.now().isoformat()}\n\n")
            f.write(f"## Prompt\n{result.prompt}\n\n")
            f.write(f"## Generated Text\n")
        
        f.write(result.text)
    
    return output_path
