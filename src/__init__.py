"""
CodeLlama Custom Setup

A customizable CodeLlama installation with flexible configuration options
for code generation tasks.
"""

from .config_manager import ConfigManager, ConfigurationError
from .codellama_wrapper import CodeLlamaWrapper, GenerationResult

__version__ = "1.0.0"
__author__ = "CodeLlama Setup"

__all__ = [
    "ConfigManager",
    "ConfigurationError", 
    "CodeLlamaWrapper",
    "GenerationResult"
]
