# Default CodeLlama Configuration
# This file contains the default settings for the CodeLlama setup

# Model Configuration
model:
  # Model name/path - can be local path or HuggingFace model ID
  name: "codellama/CodeLlama-7b-Python-hf"
  # Alternative models you can try:
  # - "codellama/CodeLlama-7b-hf"
  # - "codellama/CodeLlama-13b-Python-hf"
  # - "codellama/CodeLlama-34b-Python-hf"
  
  # Device configuration
  device: "auto"  # "auto", "cpu", "cuda", "mps"
  device_map: "auto"  # For multi-GPU setups
  
  # Model loading options
  torch_dtype: "auto"  # "auto", "float16", "bfloat16", "float32"
  load_in_8bit: false
  load_in_4bit: false
  trust_remote_code: false
  
  # Cache directory for downloaded models
  cache_dir: null  # Uses default HuggingFace cache if null

# Generation Parameters
generation:
  # Core generation settings
  max_new_tokens: 512
  max_length: null  # If set, overrides max_new_tokens
  
  # Sampling parameters
  temperature: 0.1
  top_p: 0.95
  top_k: 50
  repetition_penalty: 1.1
  
  # Special tokens
  do_sample: true
  pad_token_id: null  # Will be set automatically if null
  eos_token_id: null  # Will be set automatically if null
  
  # Stopping criteria
  stop_sequences: []  # List of strings to stop generation
  
  # Advanced options
  num_beams: 1
  early_stopping: false
  length_penalty: 1.0

# Prompt Templates
prompts:
  # System prompt for code generation
  system_prompt: |
    You are a helpful AI assistant specialized in writing high-quality code.
    You provide clear, efficient, and well-documented code solutions.
    Always follow best practices and include appropriate comments.
  
  # Template for code completion tasks
  code_completion_template: |
    {system_prompt}
    
    Complete the following code:
    ```{language}
    {code}
    ```
    
    Completed code:
  
  # Template for code explanation tasks
  code_explanation_template: |
    {system_prompt}
    
    Explain the following code:
    ```{language}
    {code}
    ```
    
    Explanation:
  
  # Template for code generation from description
  code_generation_template: |
    {system_prompt}
    
    Write {language} code for the following task:
    {description}
    
    Requirements:
    {requirements}
    
    Code:
  
  # Template for debugging assistance
  debug_template: |
    {system_prompt}
    
    Help debug the following code:
    ```{language}
    {code}
    ```
    
    Error/Issue: {error}
    
    Solution:

# Input/Output Formatting
formatting:
  # Code block formatting
  code_block_style: "markdown"  # "markdown", "plain", "highlighted"
  
  # Language detection
  auto_detect_language: true
  default_language: "python"
  
  # Output formatting
  include_explanations: true
  include_comments: true
  strip_extra_whitespace: true
  
  # Response structure
  response_format: "structured"  # "structured", "plain", "json"

# Logging and Debugging
logging:
  level: "INFO"  # "DEBUG", "INFO", "WARNING", "ERROR"
  log_file: null  # Path to log file, null for console only
  log_generation_details: false
  log_timing: true

# Performance Settings
performance:
  # Batch processing
  batch_size: 1
  
  # Memory management
  max_memory_usage: "auto"  # "auto" or specific value like "8GB"
  cleanup_after_generation: true
  
  # Caching
  enable_cache: true
  cache_size: 100  # Number of recent generations to cache

# Safety and Filtering
safety:
  # Content filtering
  filter_harmful_content: true
  filter_personal_info: true
  
  # Code safety
  warn_dangerous_code: true
  block_system_commands: true
  
  # Rate limiting
  max_requests_per_minute: 60
  max_tokens_per_request: 2048

# Custom Behavior Modes
modes:
  # Default mode
  default:
    temperature: 0.1
    max_new_tokens: 512
    include_explanations: true
  
  # Creative mode for more diverse outputs
  creative:
    temperature: 0.7
    top_p: 0.9
    max_new_tokens: 1024
    include_explanations: true
  
  # Precise mode for exact, deterministic outputs
  precise:
    temperature: 0.0
    do_sample: false
    max_new_tokens: 256
    include_explanations: false
  
  # Debug mode with detailed logging
  debug:
    temperature: 0.1
    max_new_tokens: 512
    include_explanations: true
    log_generation_details: true
    log_timing: true

# File Extensions and Language Mapping
languages:
  python: [".py", ".pyx", ".pyi"]
  javascript: [".js", ".jsx", ".mjs"]
  typescript: [".ts", ".tsx"]
  java: [".java"]
  cpp: [".cpp", ".cc", ".cxx", ".c++", ".hpp", ".h"]
  c: [".c", ".h"]
  csharp: [".cs"]
  go: [".go"]
  rust: [".rs"]
  php: [".php"]
  ruby: [".rb"]
  swift: [".swift"]
  kotlin: [".kt", ".kts"]
  scala: [".scala"]
  r: [".r", ".R"]
  matlab: [".m"]
  sql: [".sql"]
  html: [".html", ".htm"]
  css: [".css", ".scss", ".sass"]
  shell: [".sh", ".bash", ".zsh"]
  yaml: [".yaml", ".yml"]
  json: [".json"]
  xml: [".xml"]
  markdown: [".md", ".markdown"]
