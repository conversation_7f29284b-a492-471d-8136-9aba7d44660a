# Creative Coding Configuration
# For experimental and creative code generation with higher diversity

model:
  name: "codellama/CodeLlama-7b-hf"
  device: "auto"
  torch_dtype: "float16"

generation:
  max_new_tokens: 1536
  temperature: 0.8
  top_p: 0.95
  top_k: 100
  repetition_penalty: 1.2
  do_sample: true

prompts:
  system_prompt: |
    You are a creative and innovative programmer who thinks outside the box.
    You enjoy exploring unconventional solutions and creative approaches to coding.
    You write expressive, artistic, and sometimes experimental code while maintaining
    functionality and readability.

  code_generation_template: |
    {system_prompt}
    
    Create innovative and creative code for:
    {description}
    
    Feel free to:
    - Use creative variable names and approaches
    - Implement interesting algorithms or patterns
    - Add artistic or expressive elements
    - Explore multiple solution approaches
    
    Requirements: {requirements}
    
    Creative code solution:

formatting:
  include_explanations: true
  include_comments: true
  response_format: "structured"

modes:
  # Maximum creativity
  experimental:
    temperature: 1.0
    top_p: 0.98
    max_new_tokens: 2048
    
  # Balanced creativity
  creative:
    temperature: 0.8
    top_p: 0.95
    max_new_tokens: 1536
    
  # Artistic coding
  artistic:
    temperature: 0.9
    top_p: 0.92
    max_new_tokens: 1024

safety:
  filter_harmful_content: true
  warn_dangerous_code: false  # Allow more experimental code
  block_system_commands: true
