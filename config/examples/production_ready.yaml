# Production-Ready Configuration
# Conservative settings for production code generation

model:
  name: "codellama/CodeLlama-13b-hf"
  device: "auto"
  torch_dtype: "float16"
  load_in_8bit: true  # Memory optimization for larger model

generation:
  max_new_tokens: 512
  temperature: 0.05
  top_p: 0.85
  top_k: 20
  repetition_penalty: 1.05
  do_sample: true
  num_beams: 2
  early_stopping: true

prompts:
  system_prompt: |
    You are a senior software engineer focused on writing production-quality code.
    You prioritize reliability, maintainability, security, and performance.
    You always include proper error handling, logging, and follow industry best practices.
    Your code is thoroughly tested and documented.

  code_generation_template: |
    {system_prompt}
    
    Write production-ready code for:
    {description}
    
    Requirements:
    {requirements}
    
    Ensure the code includes:
    - Comprehensive error handling
    - Input validation
    - Proper logging
    - Security considerations
    - Performance optimization
    - Clear documentation
    - Type hints (where applicable)
    
    Production code:

  debug_template: |
    {system_prompt}
    
    Analyze and fix the following production code issue:
    
    Code:
    ```{language}
    {code}
    ```
    
    Error/Issue: {error}
    
    Provide:
    1. Root cause analysis
    2. Secure and robust fix
    3. Prevention strategies
    4. Testing recommendations
    
    Solution:

formatting:
  include_explanations: true
  include_comments: true
  response_format: "structured"

safety:
  filter_harmful_content: true
  filter_personal_info: true
  warn_dangerous_code: true
  block_system_commands: true
  max_requests_per_minute: 30
  max_tokens_per_request: 1024

performance:
  batch_size: 1
  cleanup_after_generation: true
  enable_cache: true
  cache_size: 50

logging:
  level: "INFO"
  log_generation_details: true
  log_timing: true

modes:
  # Ultra-conservative for critical systems
  critical:
    temperature: 0.01
    do_sample: false
    max_new_tokens: 256
    
  # Standard production
  production:
    temperature: 0.05
    max_new_tokens: 512
    
  # Code review and analysis
  review:
    temperature: 0.1
    max_new_tokens: 800
    include_explanations: true
