# Python-focused CodeLlama Configuration
# Optimized for Python development with enhanced prompts and settings

model:
  name: "codellama/CodeLlama-7b-Python-hf"
  device: "auto"
  torch_dtype: "float16"
  load_in_8bit: false

generation:
  max_new_tokens: 1024
  temperature: 0.15
  top_p: 0.9
  top_k: 40
  repetition_penalty: 1.05

prompts:
  system_prompt: |
    You are an expert Python developer with deep knowledge of Python best practices,
    design patterns, and the Python ecosystem. You write clean, efficient, and 
    well-documented Python code following PEP 8 standards. You always include 
    appropriate type hints and docstrings.

  code_completion_template: |
    {system_prompt}
    
    Complete the following Python code. Follow Python best practices and include
    appropriate type hints and docstrings where needed:
    
    ```python
    {code}
    ```
    
    Completed Python code:

  code_generation_template: |
    {system_prompt}
    
    Write Python code for the following task:
    {description}
    
    Requirements:
    {requirements}
    
    Please include:
    - Proper type hints
    - Comprehensive docstrings
    - Error handling where appropriate
    - Unit tests if requested
    
    Python code:

formatting:
  default_language: "python"
  include_explanations: true
  include_comments: true
  response_format: "structured"

modes:
  # Standard Python development
  standard:
    temperature: 0.1
    max_new_tokens: 512
    include_explanations: true
  
  # For data science and analysis
  data_science:
    temperature: 0.2
    max_new_tokens: 1024
    include_explanations: true
  
  # For web development
  web_dev:
    temperature: 0.15
    max_new_tokens: 800
    include_explanations: true
  
  # For testing and debugging
  testing:
    temperature: 0.05
    max_new_tokens: 600
    include_explanations: true

# Python-specific language extensions
languages:
  python: [".py", ".pyx", ".pyi", ".pyw"]
  jupyter: [".ipynb"]
  requirements: [".txt"]
  setup: [".py", ".cfg", ".toml"]
