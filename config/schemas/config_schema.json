{"$schema": "http://json-schema.org/draft-07/schema#", "title": "CodeLlama Configuration Schema", "description": "Schema for validating CodeLlama configuration files", "type": "object", "properties": {"model": {"type": "object", "properties": {"name": {"type": "string", "description": "Model name or path"}, "device": {"type": "string", "enum": ["auto", "cpu", "cuda", "mps"], "default": "auto"}, "device_map": {"type": ["string", "object"], "default": "auto"}, "torch_dtype": {"type": "string", "enum": ["auto", "float16", "bfloat16", "float32"], "default": "auto"}, "load_in_8bit": {"type": "boolean", "default": false}, "load_in_4bit": {"type": "boolean", "default": false}, "trust_remote_code": {"type": "boolean", "default": false}, "cache_dir": {"type": ["string", "null"], "default": null}}, "required": ["name"], "additionalProperties": false}, "generation": {"type": "object", "properties": {"max_new_tokens": {"type": "integer", "minimum": 1, "maximum": 4096, "default": 512}, "max_length": {"type": ["integer", "null"], "minimum": 1, "default": null}, "temperature": {"type": "number", "minimum": 0.0, "maximum": 2.0, "default": 0.1}, "top_p": {"type": "number", "minimum": 0.0, "maximum": 1.0, "default": 0.95}, "top_k": {"type": "integer", "minimum": 1, "default": 50}, "repetition_penalty": {"type": "number", "minimum": 0.1, "maximum": 2.0, "default": 1.1}, "do_sample": {"type": "boolean", "default": true}, "pad_token_id": {"type": ["integer", "null"], "default": null}, "eos_token_id": {"type": ["integer", "null"], "default": null}, "stop_sequences": {"type": "array", "items": {"type": "string"}, "default": []}, "num_beams": {"type": "integer", "minimum": 1, "default": 1}, "early_stopping": {"type": "boolean", "default": false}, "length_penalty": {"type": "number", "default": 1.0}}, "additionalProperties": false}, "prompts": {"type": "object", "properties": {"system_prompt": {"type": "string"}, "code_completion_template": {"type": "string"}, "code_explanation_template": {"type": "string"}, "code_generation_template": {"type": "string"}, "debug_template": {"type": "string"}}, "additionalProperties": true}, "formatting": {"type": "object", "properties": {"code_block_style": {"type": "string", "enum": ["markdown", "plain", "highlighted"], "default": "markdown"}, "auto_detect_language": {"type": "boolean", "default": true}, "default_language": {"type": "string", "default": "python"}, "include_explanations": {"type": "boolean", "default": true}, "include_comments": {"type": "boolean", "default": true}, "strip_extra_whitespace": {"type": "boolean", "default": true}, "response_format": {"type": "string", "enum": ["structured", "plain", "json"], "default": "structured"}}, "additionalProperties": false}, "logging": {"type": "object", "properties": {"level": {"type": "string", "enum": ["DEBUG", "INFO", "WARNING", "ERROR"], "default": "INFO"}, "log_file": {"type": ["string", "null"], "default": null}, "log_generation_details": {"type": "boolean", "default": false}, "log_timing": {"type": "boolean", "default": true}}, "additionalProperties": false}, "performance": {"type": "object", "properties": {"batch_size": {"type": "integer", "minimum": 1, "default": 1}, "max_memory_usage": {"type": ["string", "number"], "default": "auto"}, "cleanup_after_generation": {"type": "boolean", "default": true}, "enable_cache": {"type": "boolean", "default": true}, "cache_size": {"type": "integer", "minimum": 1, "default": 100}}, "additionalProperties": false}, "safety": {"type": "object", "properties": {"filter_harmful_content": {"type": "boolean", "default": true}, "filter_personal_info": {"type": "boolean", "default": true}, "warn_dangerous_code": {"type": "boolean", "default": true}, "block_system_commands": {"type": "boolean", "default": true}, "max_requests_per_minute": {"type": "integer", "minimum": 1, "default": 60}, "max_tokens_per_request": {"type": "integer", "minimum": 1, "default": 2048}}, "additionalProperties": false}, "modes": {"type": "object", "patternProperties": {"^[a-zA-Z_][a-zA-Z0-9_]*$": {"type": "object", "additionalProperties": true}}, "additionalProperties": false}, "languages": {"type": "object", "patternProperties": {"^[a-zA-Z_][a-zA-Z0-9_]*$": {"type": "array", "items": {"type": "string", "pattern": "^\\.[a-zA-Z0-9+#-]+$"}}}, "additionalProperties": false}}, "required": ["model", "generation"], "additionalProperties": false}