# Core ML/AI dependencies
torch>=2.0.0
transformers>=4.30.0
accelerate>=0.20.0
bitsandbytes>=0.39.0

# CodeLlama specific
codellama

# Configuration and data handling
pyyaml>=6.0
jsonschema>=4.17.0
pydantic>=2.0.0

# CLI and interaction
click>=8.0.0
rich>=13.0.0
prompt-toolkit>=3.0.0

# Utilities
tqdm>=4.65.0
numpy>=1.24.0
pandas>=2.0.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Optional: For advanced features
# sentence-transformers>=2.2.0  # For semantic search
# faiss-cpu>=1.7.0              # For vector search
# gradio>=3.35.0                # For web interface
