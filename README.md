# CodeLlama Custom Setup

A customizable CodeLlama installation with flexible configuration options for code generation tasks.

## Project Structure

```
codellama-setup/
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── setup.py                 # Package setup
├── config/                  # Configuration files
│   ├── default_config.yaml  # Default model configuration
│   ├── examples/            # Example configurations
│   └── schemas/             # Configuration schemas
├── src/                     # Source code
│   ├── __init__.py
│   ├── codellama_wrapper.py # Main CodeLlama wrapper class
│   ├── config_manager.py    # Configuration management
│   └── utils.py             # Utility functions
├── scripts/                 # Interactive scripts and CLI tools
│   ├── interactive_chat.py  # Interactive code generation
│   ├── batch_process.py     # Batch processing script
│   └── cli.py              # Command-line interface
├── examples/                # Usage examples
│   ├── basic_usage.py       # Basic usage examples
│   ├── custom_configs.py    # Custom configuration examples
│   └── advanced_usage.py    # Advanced usage patterns
├── tests/                   # Test files
│   ├── test_wrapper.py      # Test the wrapper
│   ├── test_config.py       # Test configuration
│   └── test_integration.py  # Integration tests
└── docs/                    # Documentation
    ├── installation.md      # Installation guide
    ├── configuration.md     # Configuration guide
    ├── usage.md             # Usage guide
    └── customization.md     # Customization guide
```

## Features

- **Flexible Configuration**: YAML-based configuration system for easy customization
- **Model Parameter Control**: Adjust temperature, max tokens, top-p, and other parameters
- **Behavior Customization**: Configure the model for different code generation tasks
- **Input/Output Formatting**: Customizable prompt templates and output formatting
- **Interactive Interface**: CLI and interactive scripts for easy usage
- **Extensible Design**: Easy to extend with new features and configurations

## Quick Start

1. Install dependencies: `pip install -r requirements.txt`
2. Run interactive chat: `python scripts/interactive_chat.py`
3. Use CLI: `python scripts/cli.py --help`

## Configuration

The system uses YAML configuration files to control model behavior. See `config/default_config.yaml` for the default settings and `config/examples/` for example configurations.

## Documentation

Detailed documentation is available in the `docs/` directory:
- [Installation Guide](docs/installation.md)
- [Configuration Guide](docs/configuration.md)
- [Usage Guide](docs/usage.md)
- [Customization Guide](docs/customization.md)
