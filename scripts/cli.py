#!/usr/bin/env python3
"""
Command Line Interface for CodeLlama

This script provides a command-line interface for CodeLlama with support
for various code generation tasks, batch processing, and configuration management.
"""

import os
import sys
import json
from pathlib import Path
from typing import Optional

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from codellama_wrapper import CodeLlamaWrapper
from config_manager import ConfigManager
from utils import (
    load_file_content, save_generation_result, format_generation_time,
    get_system_info, detect_file_language
)
import click


@click.group()
@click.option('--config', '-c', help='Path to configuration file')
@click.option('--mode', '-m', default='default', help='Configuration mode to use')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.pass_context
def cli(ctx, config, mode, verbose):
    """CodeLlama Command Line Interface"""
    ctx.ensure_object(dict)
    ctx.obj['config_path'] = config
    ctx.obj['mode'] = mode
    ctx.obj['verbose'] = verbose


@cli.command()
@click.argument('prompt', required=True)
@click.option('--output', '-o', help='Output file path')
@click.option('--temperature', '-t', type=float, help='Generation temperature')
@click.option('--max-tokens', type=int, help='Maximum tokens to generate')
@click.option('--language', '-l', help='Programming language')
@click.pass_context
def generate(ctx, prompt, output, temperature, max_tokens, language):
    """Generate code from a text prompt"""
    try:
        wrapper = CodeLlamaWrapper(ctx.obj['config_path'], ctx.obj['mode'])
        
        # Override parameters if provided
        kwargs = {}
        if temperature is not None:
            kwargs['temperature'] = temperature
        if max_tokens is not None:
            kwargs['max_new_tokens'] = max_tokens
        
        if ctx.obj['verbose']:
            click.echo("Loading model...")
        
        wrapper.load_model()
        
        if ctx.obj['verbose']:
            click.echo(f"Generating code for: {prompt[:50]}...")
        
        result = wrapper.generate_code(prompt, language=language, **kwargs)
        
        if output:
            output_path = Path(output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w') as f:
                f.write(result.text)
            click.echo(f"Output saved to: {output_path}")
        else:
            click.echo("\n" + "="*50)
            click.echo("GENERATED CODE:")
            click.echo("="*50)
            click.echo(result.text)
            click.echo("="*50)
        
        if ctx.obj['verbose']:
            click.echo(f"Generated {result.token_count} tokens in {format_generation_time(result.generation_time)}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('code', required=True)
@click.option('--output', '-o', help='Output file path')
@click.option('--language', '-l', help='Programming language')
@click.pass_context
def complete(ctx, code, output, language):
    """Complete partial code"""
    try:
        wrapper = CodeLlamaWrapper(ctx.obj['config_path'], ctx.obj['mode'])
        
        if ctx.obj['verbose']:
            click.echo("Loading model...")
        
        wrapper.load_model()
        
        if ctx.obj['verbose']:
            click.echo("Completing code...")
        
        result = wrapper.complete_code(code, language=language)
        
        if output:
            output_path = Path(output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w') as f:
                f.write(result.text)
            click.echo(f"Output saved to: {output_path}")
        else:
            click.echo("\n" + "="*50)
            click.echo("COMPLETED CODE:")
            click.echo("="*50)
            click.echo(result.text)
            click.echo("="*50)
        
        if ctx.obj['verbose']:
            click.echo(f"Generated {result.token_count} tokens in {format_generation_time(result.generation_time)}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('code', required=True)
@click.option('--output', '-o', help='Output file path')
@click.option('--language', '-l', help='Programming language')
@click.pass_context
def explain(ctx, code, output, language):
    """Explain code functionality"""
    try:
        wrapper = CodeLlamaWrapper(ctx.obj['config_path'], ctx.obj['mode'])
        
        if ctx.obj['verbose']:
            click.echo("Loading model...")
        
        wrapper.load_model()
        
        if ctx.obj['verbose']:
            click.echo("Explaining code...")
        
        result = wrapper.explain_code(code, language=language)
        
        if output:
            output_path = Path(output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w') as f:
                f.write(result.text)
            click.echo(f"Output saved to: {output_path}")
        else:
            click.echo("\n" + "="*50)
            click.echo("CODE EXPLANATION:")
            click.echo("="*50)
            click.echo(result.text)
            click.echo("="*50)
        
        if ctx.obj['verbose']:
            click.echo(f"Generated {result.token_count} tokens in {format_generation_time(result.generation_time)}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('file_path', type=click.Path(exists=True))
@click.option('--output', '-o', help='Output file path')
@click.option('--task', type=click.Choice(['complete', 'explain', 'debug']), 
              default='explain', help='Task to perform on the file')
@click.option('--error', help='Error message for debug task')
@click.pass_context
def process_file(ctx, file_path, output, task, error):
    """Process a code file"""
    try:
        content, language = load_file_content(file_path)
        
        wrapper = CodeLlamaWrapper(ctx.obj['config_path'], ctx.obj['mode'])
        
        if ctx.obj['verbose']:
            click.echo(f"Loading model...")
            click.echo(f"Processing {file_path} (detected language: {language})")
        
        wrapper.load_model()
        
        if task == 'complete':
            result = wrapper.complete_code(content, language=language)
        elif task == 'explain':
            result = wrapper.explain_code(content, language=language)
        elif task == 'debug':
            if not error:
                error = click.prompt("Enter error message or description")
            result = wrapper.debug_code(content, error, language=language)
        
        if output:
            output_path = Path(output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w') as f:
                f.write(result.text)
            click.echo(f"Output saved to: {output_path}")
        else:
            click.echo("\n" + "="*50)
            click.echo(f"{task.upper()} RESULT:")
            click.echo("="*50)
            click.echo(result.text)
            click.echo("="*50)
        
        if ctx.obj['verbose']:
            click.echo(f"Generated {result.token_count} tokens in {format_generation_time(result.generation_time)}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('input_dir', type=click.Path(exists=True))
@click.argument('output_dir', type=click.Path())
@click.option('--task', type=click.Choice(['complete', 'explain']), 
              default='explain', help='Task to perform on files')
@click.option('--pattern', default='*.py', help='File pattern to match')
@click.pass_context
def batch(ctx, input_dir, output_dir, task, pattern):
    """Process multiple files in batch"""
    try:
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Find files matching pattern
        files = list(input_path.glob(pattern))
        if not files:
            click.echo(f"No files found matching pattern: {pattern}")
            return
        
        wrapper = CodeLlamaWrapper(ctx.obj['config_path'], ctx.obj['mode'])
        
        if ctx.obj['verbose']:
            click.echo("Loading model...")
        
        wrapper.load_model()
        
        click.echo(f"Processing {len(files)} files...")
        
        for file_path in files:
            try:
                if ctx.obj['verbose']:
                    click.echo(f"Processing: {file_path.name}")
                
                content, language = load_file_content(file_path)
                
                if task == 'complete':
                    result = wrapper.complete_code(content, language=language)
                elif task == 'explain':
                    result = wrapper.explain_code(content, language=language)
                
                # Save result
                output_file = output_path / f"{file_path.stem}_{task}.txt"
                with open(output_file, 'w') as f:
                    f.write(f"# {task.title()} for {file_path.name}\n\n")
                    f.write(result.text)
                
                if ctx.obj['verbose']:
                    click.echo(f"  → {output_file}")
            
            except Exception as e:
                click.echo(f"Error processing {file_path}: {e}", err=True)
        
        click.echo(f"Batch processing complete. Results saved to: {output_path}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def info(ctx):
    """Show system and model information"""
    try:
        # System info
        sys_info = get_system_info()
        
        click.echo("System Information:")
        click.echo("-" * 20)
        for key, value in sys_info.items():
            click.echo(f"{key}: {value}")
        
        # Configuration info
        config_manager = ConfigManager()
        config = config_manager.load_config(ctx.obj['config_path'])
        config = config_manager.get_mode_config(config, ctx.obj['mode'])
        
        click.echo("\nConfiguration:")
        click.echo("-" * 15)
        click.echo(f"Model: {config['model']['name']}")
        click.echo(f"Mode: {ctx.obj['mode']}")
        click.echo(f"Temperature: {config['generation']['temperature']}")
        click.echo(f"Max Tokens: {config['generation']['max_new_tokens']}")
        click.echo(f"Device: {config['model']['device']}")
        
        # Available modes
        modes = config_manager.list_available_modes(config)
        click.echo(f"\nAvailable Modes: {', '.join(modes)}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('output_path', type=click.Path())
@click.pass_context
def create_config(ctx, output_path):
    """Create an example configuration file"""
    try:
        config_manager = ConfigManager()
        config_manager.create_example_config(output_path)
        click.echo(f"Example configuration created: {output_path}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


if __name__ == "__main__":
    cli()
