#!/usr/bin/env python3
"""
Batch Processing Script for CodeLlama

This script provides batch processing capabilities for CodeLlama,
allowing you to process multiple files or prompts in sequence.
"""

import os
import sys
import json
import csv
from pathlib import Path
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from codellama_wrapper import CodeLlamaWrapper
from utils import load_file_content, save_generation_result, format_generation_time
import click


class BatchProcessor:
    """Handles batch processing of CodeLlama tasks."""
    
    def __init__(self, config_path=None, mode="default", max_workers=1):
        self.config_path = config_path
        self.mode = mode
        self.max_workers = max_workers
        self.wrapper = None
        self.results = []
    
    def initialize_model(self):
        """Initialize the CodeLlama model."""
        if self.wrapper is None:
            self.wrapper = CodeLlamaWrapper(self.config_path, self.mode)
            self.wrapper.load_model()
    
    def process_single_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single task."""
        self.initialize_model()
        
        task_type = task.get('type', 'generate')
        start_time = time.time()
        
        try:
            if task_type == 'generate':
                result = self.wrapper.generate_code(
                    task['prompt'],
                    language=task.get('language'),
                    requirements=task.get('requirements', '')
                )
            elif task_type == 'complete':
                result = self.wrapper.complete_code(
                    task['code'],
                    language=task.get('language')
                )
            elif task_type == 'explain':
                result = self.wrapper.explain_code(
                    task['code'],
                    language=task.get('language')
                )
            elif task_type == 'debug':
                result = self.wrapper.debug_code(
                    task['code'],
                    task['error'],
                    language=task.get('language')
                )
            else:
                raise ValueError(f"Unknown task type: {task_type}")
            
            return {
                'task_id': task.get('id', ''),
                'task_type': task_type,
                'status': 'success',
                'result': result.text,
                'token_count': result.token_count,
                'generation_time': result.generation_time,
                'total_time': time.time() - start_time,
                'input': task
            }
        
        except Exception as e:
            return {
                'task_id': task.get('id', ''),
                'task_type': task_type,
                'status': 'error',
                'error': str(e),
                'total_time': time.time() - start_time,
                'input': task
            }
    
    def process_tasks(self, tasks: List[Dict[str, Any]], parallel=False) -> List[Dict[str, Any]]:
        """Process a list of tasks."""
        results = []
        
        if parallel and self.max_workers > 1:
            # Parallel processing (experimental - may have memory issues)
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_task = {
                    executor.submit(self.process_single_task, task): task 
                    for task in tasks
                }
                
                for future in as_completed(future_to_task):
                    result = future.result()
                    results.append(result)
        else:
            # Sequential processing (recommended)
            for i, task in enumerate(tasks):
                click.echo(f"Processing task {i+1}/{len(tasks)}: {task.get('id', f'task_{i+1}')}")
                result = self.process_single_task(task)
                results.append(result)
        
        return results
    
    def save_results(self, results: List[Dict[str, Any]], output_path: Path, format='json'):
        """Save results to file."""
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if format == 'json':
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2)
        elif format == 'csv':
            if results:
                fieldnames = ['task_id', 'task_type', 'status', 'token_count', 
                             'generation_time', 'total_time', 'error']
                
                with open(output_path, 'w', newline='') as f:
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    
                    for result in results:
                        row = {k: result.get(k, '') for k in fieldnames}
                        writer.writerow(row)
        
        # Also save individual results as text files
        results_dir = output_path.parent / f"{output_path.stem}_results"
        results_dir.mkdir(exist_ok=True)
        
        for result in results:
            if result['status'] == 'success':
                result_file = results_dir / f"{result['task_id']}.txt"
                with open(result_file, 'w') as f:
                    f.write(f"# Task: {result['task_id']}\n")
                    f.write(f"# Type: {result['task_type']}\n")
                    f.write(f"# Tokens: {result.get('token_count', 'N/A')}\n")
                    f.write(f"# Time: {format_generation_time(result.get('generation_time', 0))}\n\n")
                    f.write(result['result'])


@click.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--output', '-o', required=True, help='Output file path')
@click.option('--config', '-c', help='Path to configuration file')
@click.option('--mode', '-m', default='default', help='Configuration mode to use')
@click.option('--format', type=click.Choice(['json', 'csv']), default='json', 
              help='Output format')
@click.option('--parallel', is_flag=True, help='Enable parallel processing (experimental)')
@click.option('--max-workers', type=int, default=1, help='Maximum worker threads')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
def main(input_file, output, config, mode, format, parallel, max_workers, verbose):
    """
    Batch process CodeLlama tasks from a JSON file.
    
    Input file should contain a JSON array of task objects. Each task should have:
    - type: "generate", "complete", "explain", or "debug"
    - id: unique identifier for the task
    - Additional fields depending on task type:
      - generate: prompt, language (optional), requirements (optional)
      - complete: code, language (optional)
      - explain: code, language (optional)
      - debug: code, error, language (optional)
    
    Example input file:
    [
        {
            "id": "task1",
            "type": "generate",
            "prompt": "Create a function to calculate fibonacci numbers",
            "language": "python"
        },
        {
            "id": "task2", 
            "type": "explain",
            "code": "def fib(n): return n if n <= 1 else fib(n-1) + fib(n-2)",
            "language": "python"
        }
    ]
    """
    try:
        # Load tasks from input file
        with open(input_file, 'r') as f:
            tasks = json.load(f)
        
        if not isinstance(tasks, list):
            raise ValueError("Input file must contain a JSON array of tasks")
        
        if verbose:
            click.echo(f"Loaded {len(tasks)} tasks from {input_file}")
        
        # Initialize batch processor
        processor = BatchProcessor(config, mode, max_workers)
        
        if verbose:
            click.echo(f"Initializing model (mode: {mode})...")
        
        # Process tasks
        start_time = time.time()
        results = processor.process_tasks(tasks, parallel=parallel)
        total_time = time.time() - start_time
        
        # Save results
        output_path = Path(output)
        processor.save_results(results, output_path, format)
        
        # Print summary
        successful = sum(1 for r in results if r['status'] == 'success')
        failed = len(results) - successful
        total_tokens = sum(r.get('token_count', 0) for r in results if r['status'] == 'success')
        
        click.echo(f"\nBatch processing complete!")
        click.echo(f"Total time: {format_generation_time(total_time)}")
        click.echo(f"Tasks processed: {len(results)}")
        click.echo(f"Successful: {successful}")
        click.echo(f"Failed: {failed}")
        click.echo(f"Total tokens generated: {total_tokens}")
        click.echo(f"Results saved to: {output_path}")
        
        if failed > 0:
            click.echo(f"\nFailed tasks:")
            for result in results:
                if result['status'] == 'error':
                    click.echo(f"  {result['task_id']}: {result['error']}")
    
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
