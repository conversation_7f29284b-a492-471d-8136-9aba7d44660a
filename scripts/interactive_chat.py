#!/usr/bin/env python3
"""
Interactive Chat Interface for CodeLlama

This script provides an interactive command-line interface for chatting
with CodeLlama models. It supports various commands and modes for different
types of code generation tasks.
"""

import os
import sys
import argparse
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.syntax import Syntax
    from rich.table import Table
    from rich.prompt import Prompt, Confirm
    from rich.text import Text
    import click
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Warning: rich library not available. Install with: pip install rich")
    import click

from codellama_wrapper import CodeLlamaWrapper
from utils import format_code_block, extract_code_from_response, get_system_info
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.text import Text
import click


class InteractiveChat:
    """Interactive chat interface for Code<PERSON>lama."""
    
    def __init__(self, config_path=None, mode="default"):
        self.console = Console()
        self.wrapper = None
        self.config_path = config_path
        self.mode = mode
        self.session_history = []
        
    def initialize_model(self):
        """Initialize the CodeLlama model."""
        try:
            self.console.print("[yellow]Initializing CodeLlama...[/yellow]")
            self.wrapper = CodeLlamaWrapper(self.config_path, self.mode)
            self.wrapper.load_model()
            self.console.print("[green]✓ Model loaded successfully![/green]")
            return True
        except Exception as e:
            self.console.print(f"[red]✗ Failed to load model: {e}[/red]")
            return False
    
    def show_welcome(self):
        """Display welcome message and instructions."""
        welcome_text = """
[bold blue]CodeLlama Interactive Chat[/bold blue]

Available commands:
• [bold]/help[/bold] - Show this help message
• [bold]/complete <code>[/bold] - Complete partial code
• [bold]/explain <code>[/bold] - Explain code
• [bold]/generate <description>[/bold] - Generate code from description
• [bold]/debug <code> <error>[/bold] - Debug code with error
• [bold]/mode <mode_name>[/bold] - Switch to different mode
• [bold]/modes[/bold] - List available modes
• [bold]/stats[/bold] - Show generation statistics
• [bold]/save <filename>[/bold] - Save last response to file
• [bold]/load <filename>[/bold] - Load code from file
• [bold]/clear[/bold] - Clear session history
• [bold]/config[/bold] - Show current configuration
• [bold]/quit[/bold] or [bold]/exit[/bold] - Exit the chat

You can also just type your question or code directly!
        """
        
        panel = Panel(welcome_text, title="Welcome", border_style="blue")
        self.console.print(panel)
    
    def show_help(self):
        """Show help information."""
        self.show_welcome()
    
    def show_modes(self):
        """Show available modes."""
        if not self.wrapper:
            self.console.print("[red]Model not initialized[/red]")
            return
        
        modes = self.wrapper.config_manager.list_available_modes(self.wrapper.config)
        
        table = Table(title="Available Modes")
        table.add_column("Mode", style="cyan")
        table.add_column("Description", style="white")
        
        mode_descriptions = {
            "default": "Balanced settings for general code generation",
            "creative": "Higher temperature for more diverse outputs",
            "precise": "Deterministic outputs with low temperature",
            "debug": "Detailed logging and debugging information"
        }
        
        for mode in modes:
            description = mode_descriptions.get(mode, "Custom mode")
            table.add_row(mode, description)
        
        self.console.print(table)
    
    def show_stats(self):
        """Show generation statistics."""
        if not self.wrapper:
            self.console.print("[red]Model not initialized[/red]")
            return
        
        stats = self.wrapper.get_stats()
        
        table = Table(title="Generation Statistics")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="white")
        
        table.add_row("Total Generations", str(stats["total_generations"]))
        table.add_row("Total Tokens", str(stats["total_tokens"]))
        table.add_row("Total Time", f"{stats['total_time']:.2f}s")
        
        if stats["total_generations"] > 0:
            avg_time = stats["total_time"] / stats["total_generations"]
            avg_tokens = stats["total_tokens"] / stats["total_generations"]
            table.add_row("Avg Time/Generation", f"{avg_time:.2f}s")
            table.add_row("Avg Tokens/Generation", f"{avg_tokens:.1f}")
        
        self.console.print(table)
    
    def show_config(self):
        """Show current configuration."""
        if not self.wrapper:
            self.console.print("[red]Model not initialized[/red]")
            return
        
        config = self.wrapper.config
        
        table = Table(title="Current Configuration")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="white")
        
        # Show key configuration values
        table.add_row("Model", config["model"]["name"])
        table.add_row("Mode", self.mode)
        table.add_row("Temperature", str(config["generation"]["temperature"]))
        table.add_row("Max Tokens", str(config["generation"]["max_new_tokens"]))
        table.add_row("Top P", str(config["generation"]["top_p"]))
        table.add_row("Device", config["model"]["device"])
        
        self.console.print(table)
    
    def switch_mode(self, mode_name):
        """Switch to a different mode."""
        try:
            # Reinitialize with new mode
            self.mode = mode_name
            self.wrapper = CodeLlamaWrapper(self.config_path, mode_name)
            self.console.print(f"[green]✓ Switched to mode: {mode_name}[/green]")
        except Exception as e:
            self.console.print(f"[red]✗ Failed to switch mode: {e}[/red]")
    
    def save_response(self, filename, content):
        """Save response to file."""
        try:
            with open(filename, 'w') as f:
                f.write(content)
            self.console.print(f"[green]✓ Saved to {filename}[/green]")
        except Exception as e:
            self.console.print(f"[red]✗ Failed to save: {e}[/red]")
    
    def load_file(self, filename):
        """Load code from file."""
        try:
            with open(filename, 'r') as f:
                content = f.read()
            self.console.print(f"[green]✓ Loaded {filename}[/green]")
            return content
        except Exception as e:
            self.console.print(f"[red]✗ Failed to load: {e}[/red]")
            return None
    
    def display_response(self, result):
        """Display generation result with syntax highlighting."""
        # Extract code blocks from response
        code_blocks = extract_code_from_response(result.text)
        
        if code_blocks:
            for language, code in code_blocks:
                if language:
                    syntax = Syntax(code, language, theme="monokai", line_numbers=True)
                    self.console.print(Panel(syntax, title=f"Generated {language.title()} Code"))
                else:
                    self.console.print(Panel(code, title="Generated Code"))
        else:
            # No code blocks found, display as plain text
            self.console.print(Panel(result.text, title="Response"))
        
        # Show generation info
        info_text = f"[dim]Generated {result.token_count} tokens in {result.generation_time:.2f}s[/dim]"
        self.console.print(info_text)
    
    def process_command(self, user_input):
        """Process user commands."""
        if not user_input.startswith('/'):
            return False
        
        parts = user_input[1:].split(' ', 1)
        command = parts[0].lower()
        args = parts[1] if len(parts) > 1 else ""
        
        if command in ['help', 'h']:
            self.show_help()
        elif command == 'modes':
            self.show_modes()
        elif command == 'stats':
            self.show_stats()
        elif command == 'config':
            self.show_config()
        elif command == 'clear':
            self.session_history.clear()
            self.console.print("[green]✓ Session history cleared[/green]")
        elif command == 'mode':
            if args:
                self.switch_mode(args.strip())
            else:
                self.console.print("[red]Please specify a mode name[/red]")
        elif command == 'save':
            if args and self.session_history:
                last_response = self.session_history[-1]['response']
                self.save_response(args.strip(), last_response)
            else:
                self.console.print("[red]No response to save or filename not specified[/red]")
        elif command == 'load':
            if args:
                content = self.load_file(args.strip())
                if content:
                    return content
        elif command == 'complete':
            if args:
                return self.handle_completion(args)
        elif command == 'explain':
            if args:
                return self.handle_explanation(args)
        elif command == 'generate':
            if args:
                return self.handle_generation(args)
        elif command == 'debug':
            return self.handle_debug(args)
        elif command in ['quit', 'exit', 'q']:
            return 'quit'
        else:
            self.console.print(f"[red]Unknown command: {command}[/red]")
        
        return True
    
    def handle_completion(self, code):
        """Handle code completion."""
        try:
            result = self.wrapper.complete_code(code)
            self.display_response(result)
            self.session_history.append({
                'type': 'completion',
                'input': code,
                'response': result.text
            })
        except Exception as e:
            self.console.print(f"[red]Error: {e}[/red]")
    
    def handle_explanation(self, code):
        """Handle code explanation."""
        try:
            result = self.wrapper.explain_code(code)
            self.display_response(result)
            self.session_history.append({
                'type': 'explanation',
                'input': code,
                'response': result.text
            })
        except Exception as e:
            self.console.print(f"[red]Error: {e}[/red]")
    
    def handle_generation(self, description):
        """Handle code generation."""
        try:
            result = self.wrapper.generate_code(description)
            self.display_response(result)
            self.session_history.append({
                'type': 'generation',
                'input': description,
                'response': result.text
            })
        except Exception as e:
            self.console.print(f"[red]Error: {e}[/red]")
    
    def handle_debug(self, args):
        """Handle code debugging."""
        if not args:
            code = Prompt.ask("Enter the code to debug")
            error = Prompt.ask("Enter the error message or description")
        else:
            # Try to split args into code and error
            parts = args.split(' ', 1)
            if len(parts) == 2:
                code, error = parts
            else:
                code = args
                error = Prompt.ask("Enter the error message or description")
        
        try:
            result = self.wrapper.debug_code(code, error)
            self.display_response(result)
            self.session_history.append({
                'type': 'debug',
                'input': f"Code: {code}\nError: {error}",
                'response': result.text
            })
        except Exception as e:
            self.console.print(f"[red]Error: {e}[/red]")
    
    def run(self):
        """Run the interactive chat."""
        self.show_welcome()
        
        if not self.initialize_model():
            return
        
        self.console.print("\n[green]Ready! Type your questions or use commands (type /help for help)[/green]\n")
        
        while True:
            try:
                user_input = Prompt.ask("[bold cyan]You[/bold cyan]").strip()
                
                if not user_input:
                    continue
                
                # Process commands
                result = self.process_command(user_input)
                
                if result == 'quit':
                    break
                elif result is True:
                    continue
                elif isinstance(result, str):
                    # Loaded content, use it as input
                    user_input = result
                
                # If not a command, treat as general query
                if not user_input.startswith('/'):
                    try:
                        result = self.wrapper.generate(user_input)
                        self.display_response(result)
                        self.session_history.append({
                            'type': 'general',
                            'input': user_input,
                            'response': result.text
                        })
                    except Exception as e:
                        self.console.print(f"[red]Error: {e}[/red]")
                
            except KeyboardInterrupt:
                if Confirm.ask("\n[yellow]Do you want to exit?[/yellow]"):
                    break
                else:
                    self.console.print()
            except EOFError:
                break
        
        self.console.print("\n[blue]Goodbye![/blue]")


@click.command()
@click.option('--config', '-c', help='Path to configuration file')
@click.option('--mode', '-m', default='default', help='Configuration mode to use')
def main(config, mode):
    """Interactive chat interface for CodeLlama."""
    chat = InteractiveChat(config, mode)
    chat.run()


if __name__ == "__main__":
    main()
